<html lang="en" class="light">
  <head>
    <!-- Global Metadata -->
    <!-- Font preloads -->
    <link
      href="https://fonts.googleapis.com/css2?family=Atkinson+Hyperlegible+Next:ital,wght@0,200..800;1,200..800&amp;family=Baskervville:ital,wght@0,400..700;1,400..700&amp;display=swap"
      rel="stylesheet"
    />
    <!-- Canonical URL -->
    <!-- Primary Meta Tags -->
    <!-- Open Graph / Facebook -->
    <!-- Twitter -->
    <link rel="stylesheet" href="/_astro/lfm.erUP-9BY.css" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content="Astro v5.13.4" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
    <link
      rel="preload"
      href="/fonts/geist-mono-variable.woff2"
      as="font"
      type="font/woff2"
      crossorigin=""
    />
    <link rel="canonical" href="https://natalie.sh/" />
    <title>natalie's blog</title>
    <meta name="title" content="natalie's blog" />
    <meta
      name="description"
      content="a blog about web development, design, and other things I find interesting"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://natalie.sh/" />
    <meta property="og:title" content="natalie's blog" />
    <meta
      property="og:description"
      content="a blog about web development, design, and other things I find interesting"
    />
    <meta
      property="og:image"
      content="https://ogimage-workers.kanbaru.workers.dev/?title=natalie%27s+blog&amp;liner=a+blog+about+web+development%2C+design%2C+and+other+things+I+find+interesting&amp;date=Aug+28%2C+2025"
    />
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://natalie.sh/" />
    <meta property="twitter:title" content="natalie's blog" />
    <meta
      property="twitter:description"
      content="a blog about web development, design, and other things I find interesting"
    />
    <meta
      property="twitter:image"
      content="https://ogimage-workers.kanbaru.workers.dev/?title=natalie%27s+blog&amp;liner=a+blog+about+web+development%2C+design%2C+and+other+things+I+find+interesting&amp;date=Aug+28%2C+2025"
    />
    <style>
      body {
        visibility: hidden;
        opacity: 0;
      }
    </style>
    <script data-astro-exec="">
      const setTheme = () => {
        let theme

        if (typeof localStorage !== "undefined" && localStorage.getItem("theme")) {
          theme = localStorage.getItem("theme")
        } else {
          theme = window.matchMedia("(prefers-color-scheme: dark)").matches
            ? "dark"
            : "light"
        }

        document.documentElement.classList[theme ? "add" : "remove"](theme)

        if (typeof localStorage !== "undefined") {
          const observer = new MutationObserver(() => {
            const isDark = document.documentElement.classList.contains("dark")
            localStorage.setItem("theme", isDark ? "dark" : "light")
          })
          observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ["class"],
          })
        }
      }

      setTheme()

      document.addEventListener("astro:page-load", () => {
        setTheme()
      })

      document.addEventListener("astro:after-swap", () => {
        setTheme()
      })
    </script>
    <meta name="astro-view-transitions-enabled" content="true" />
    <meta name="astro-view-transitions-fallback" content="animate" />
    <script
      type="module"
      src="/_astro/ClientRouter.astro_astro_type_script_index_0_lang.DZnDNxNb.js"
      data-astro-exec=""
    ></script>
    <style>
      #persist-wrapper[data-astro-cid-qu6evc55] {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -10;
        pointer-events: none;
      }
    </style>
    <style>
      /*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
      @layer properties {
        @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or
          ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
          *,
          :before,
          :after,
          ::backdrop {
            --tw-font-weight: initial;
          }
        }
      }
      a[data-astro-cid-3ef6ksr2] {
        --tw-font-weight: var(--font-weight-medium, 500);
        font-weight: var(--font-weight-medium, 500);
        color: var(--color-zinc-500, oklch(55.2% 0.016 285.938));
      }
      @media (hover: hover) {
        a[data-astro-cid-3ef6ksr2]:hover {
          color: var(--color-zinc-900, oklch(21% 0.006 285.885));
          text-decoration-line: underline;
        }
        a[data-astro-cid-3ef6ksr2]:is(.dark *):hover {
          color: var(--color-zinc-100, oklch(96.7% 0.001 286.375));
        }
      }
      @property --tw-font-weight {
        syntax: "*";
        inherits: false;
      }
      body {
        visibility: visible;
        opacity: 1;
        transition: visibility 0.2s linear, opacity 0.2s linear;
      }
      @keyframes astroFadeInOut {
        0% {
          opacity: 1;
        }
        to {
          opacity: 0;
        }
      }
      @keyframes astroFadeIn {
        0% {
          opacity: 0;
          mix-blend-mode: plus-lighter;
        }
        to {
          opacity: 1;
          mix-blend-mode: plus-lighter;
        }
      }
      @keyframes astroFadeOut {
        0% {
          opacity: 1;
          mix-blend-mode: plus-lighter;
        }
        to {
          opacity: 0;
          mix-blend-mode: plus-lighter;
        }
      }
      @keyframes astroSlideFromRight {
        0% {
          transform: translate(100%);
        }
      }
      @keyframes astroSlideFromLeft {
        0% {
          transform: translate(-100%);
        }
      }
      @keyframes astroSlideToRight {
        to {
          transform: translate(100%);
        }
      }
      @keyframes astroSlideToLeft {
        to {
          transform: translate(-100%);
        }
      }
      @media (prefers-reduced-motion) {
        ::view-transition-group(*),
        ::view-transition-old(*),
        ::view-transition-new(*) {
          animation: none !important;
        }
        [data-astro-transition-scope] {
          animation: none !important;
        }
      }
    </style>
    <style>
      [data-astro-transition-scope="astro-l7r54iwe-2"] {
        view-transition-name: header;
      }
      @layer astro {
        ::view-transition-old(header) {
          animation-duration: 180ms;
          animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
          animation-fill-mode: both;
          animation-name: astroFadeOut;
        }
        ::view-transition-new(header) {
          animation-duration: 180ms;
          animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
          animation-fill-mode: both;
          animation-name: astroFadeIn;
        }
        [data-astro-transition="back"]::view-transition-old(header) {
          animation-duration: 180ms;
          animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
          animation-fill-mode: both;
          animation-name: astroFadeOut;
        }
        [data-astro-transition="back"]::view-transition-new(header) {
          animation-duration: 180ms;
          animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
          animation-fill-mode: both;
          animation-name: astroFadeIn;
        }
      }
      [data-astro-transition-fallback="old"]
        [data-astro-transition-scope="astro-l7r54iwe-2"],
      [data-astro-transition-fallback="old"][data-astro-transition-scope="astro-l7r54iwe-2"] {
        animation-duration: 180ms;
        animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
        animation-fill-mode: both;
        animation-name: astroFadeOut;
      }
      [data-astro-transition-fallback="new"]
        [data-astro-transition-scope="astro-l7r54iwe-2"],
      [data-astro-transition-fallback="new"][data-astro-transition-scope="astro-l7r54iwe-2"] {
        animation-duration: 180ms;
        animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
        animation-fill-mode: both;
        animation-name: astroFadeIn;
      }
      [data-astro-transition="back"][data-astro-transition-fallback="old"]
        [data-astro-transition-scope="astro-l7r54iwe-2"],
      [data-astro-transition="back"][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-l7r54iwe-2"] {
        animation-duration: 180ms;
        animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
        animation-fill-mode: both;
        animation-name: astroFadeOut;
      }
      [data-astro-transition="back"][data-astro-transition-fallback="new"]
        [data-astro-transition-scope="astro-l7r54iwe-2"],
      [data-astro-transition="back"][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-l7r54iwe-2"] {
        animation-duration: 180ms;
        animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
        animation-fill-mode: both;
        animation-name: astroFadeIn;
      }
    </style>
    <style data-emotion="css" data-s=""></style>
  </head>
  <body
    class="max-h-max min-h-screen w-screen max-w-screen overflow-x-clip font-sans text-zinc-900 antialiased transition-colors dark:bg-zinc-900 dark:text-zinc-200"
    cz-shortcut-listen="true"
  >
    <div
      id="page-container"
      class="mx-auto p-4 layout-transition centered-layout max-w-lg"
      style="view-transition-name: page-container"
    >
      <header
        class="mb-6 flex items-center justify-between"
        data-astro-cid-3ef6ksr2=""
        data-astro-transition-scope="astro-l7r54iwe-2"
      >
        <nav
          class="flex items-center gap-4 text-gray-950 dark:text-gray-300"
          data-astro-cid-3ef6ksr2=""
        >
          <a href="/" data-astro-cid-3ef6ksr2=""> home </a>
          <a href="/cv" data-astro-cid-3ef6ksr2=""> work </a>
          <a href="/posts" data-astro-cid-3ef6ksr2=""> posts </a>
          <a href="/uses" data-astro-cid-3ef6ksr2=""> uses </a>
        </nav>
        <button
          id="theme-toggle"
          class="inline-flex items-center rounded-lg border p-1.5 transition-colors hover:bg-zinc-50 dark:border-zinc-700 dark:hover:bg-zinc-800 [&amp;_svg]:text-zinc-500 hover:[&amp;_svg]:text-zinc-900 dark:hover:[&amp;_svg]:text-zinc-100"
          aria-label="Toggle theme"
        >
          <span class="sr-only">Toggle theme</span>
          <svg
            id="sun"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 16 16"
            fill="currentColor"
            class="hidden size-4 dark:block"
          >
            <path
              d="M8 1a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 8 1ZM10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM12.95 4.11a.75.75 0 1 0-1.06-1.06l-1.062 1.06a.75.75 0 0 0 1.061 1.062l1.06-1.061ZM15 8a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5A.75.75 0 0 1 15 8ZM11.89 12.95a.75.75 0 0 0 1.06-1.06l-1.06-1.062a.75.75 0 0 0-1.062 1.061l1.061 1.06ZM8 12a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 8 12ZM5.172 11.89a.75.75 0 0 0-1.061-1.062L3.05 11.89a.75.75 0 1 0 1.06 1.06l1.06-1.06ZM4 8a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5A.75.75 0 0 1 4 8ZM4.11 5.172A.75.75 0 0 0 5.173 4.11L4.11 3.05a.75.75 0 1 0-1.06 1.06l1.06 1.06Z"
            ></path>
          </svg>
          <svg
            id="moon"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 16 16"
            fill="currentColor"
            class="size-4 dark:hidden"
          >
            <path
              d="M14.438 10.148c.19-.425-.321-.787-.748-.601A5.5 5.5 0 0 1 6.453 2.31c.186-.427-.176-.938-.6-.748a6.501 6.501 0 1 0 8.585 8.586Z"
            ></path>
          </svg>
        </button>
        <script type="module" data-astro-exec="">
          const t = () => {
            document.getElementById("theme-toggle")?.addEventListener("click", () => {
              document.documentElement.classList.toggle("dark")
            })
          }
          t()
          document.addEventListener("astro:after-swap", t)
        </script>
      </header>
      <svg width="0" height="0" overflow="visible">
        <filter
          id="ambilight"
          x="-500%"
          y="-500%"
          width="2000%"
          height="2000%"
          color-interpolation-filters="sRGB"
        >
          <feOffset in="SourceGraphic" result="source-copy"></feOffset>
          <feColorMatrix
            in="source-copy"
            type="saturate"
            values="1.5"
            result="saturated-copy"
          ></feColorMatrix>
          <feColorMatrix
            in="saturated-copy"
            type="matrix"
            values="1 0 0 0 0
                   0 1 0 0 0
                   0 0 1 0 0
                   33 33 33 201 -100"
            result="bright-colors"
          ></feColorMatrix>
          <feMorphology
            in="bright-colors"
            operator="dilate"
            radius="10"
            result="spread"
          ></feMorphology>
          <feGaussianBlur
            in="spread"
            stdDeviation="83"
            result="ambilight-light"
          ></feGaussianBlur>
          <feOffset in="SourceGraphic" result="source"></feOffset>
          <feComposite in="source" in2="ambilight-light" operator="over"></feComposite>
        </filter>
      </svg>
      <div
        id="persist-wrapper"
        class=""
        data-astro-cid-qu6evc55=""
        data-astro-transition-persist="astro-pbjn2ol6-1"
      >
        <style>
          astro-island,
          astro-slot,
          astro-static-slot {
            display: contents;
          }
        </style>
        <script data-astro-exec="">
          ;(() => {
            var e = async (t) => {
              await (
                await t()
              )()
            }
            ;(self.Astro || (self.Astro = {})).only = e
            window.dispatchEvent(new Event("astro:only"))
          })()
        </script>
        <script data-astro-exec="">
          ;(() => {
            var A = Object.defineProperty
            var g = (i, o, a) =>
              o in i
                ? A(i, o, { enumerable: !0, configurable: !0, writable: !0, value: a })
                : (i[o] = a)
            var d = (i, o, a) => g(i, typeof o != "symbol" ? o + "" : o, a)
            {
              let i = {
                  0: (t) => m(t),
                  1: (t) => a(t),
                  2: (t) => new RegExp(t),
                  3: (t) => new Date(t),
                  4: (t) => new Map(a(t)),
                  5: (t) => new Set(a(t)),
                  6: (t) => BigInt(t),
                  7: (t) => new URL(t),
                  8: (t) => new Uint8Array(t),
                  9: (t) => new Uint16Array(t),
                  10: (t) => new Uint32Array(t),
                  11: (t) => (1 / 0) * t,
                },
                o = (t) => {
                  let [l, e] = t
                  return l in i ? i[l](e) : void 0
                },
                a = (t) => t.map(o),
                m = (t) =>
                  typeof t != "object" || t === null
                    ? t
                    : Object.fromEntries(Object.entries(t).map(([l, e]) => [l, o(e)]))
              class y extends HTMLElement {
                constructor() {
                  super(...arguments)
                  d(this, "Component")
                  d(this, "hydrator")
                  d(this, "hydrate", async () => {
                    var b
                    if (!this.hydrator || !this.isConnected) return
                    let e =
                      (b = this.parentElement) == null
                        ? void 0
                        : b.closest("astro-island[ssr]")
                    if (e) {
                      e.addEventListener("astro:hydrate", this.hydrate, { once: !0 })
                      return
                    }
                    let c = this.querySelectorAll("astro-slot"),
                      n = {},
                      h = this.querySelectorAll("template[data-astro-template]")
                    for (let r of h) {
                      let s = r.closest(this.tagName)
                      s != null &&
                        s.isSameNode(this) &&
                        ((n[r.getAttribute("data-astro-template") || "default"] =
                          r.innerHTML),
                        r.remove())
                    }
                    for (let r of c) {
                      let s = r.closest(this.tagName)
                      s != null &&
                        s.isSameNode(this) &&
                        (n[r.getAttribute("name") || "default"] = r.innerHTML)
                    }
                    let p
                    try {
                      p = this.hasAttribute("props")
                        ? m(JSON.parse(this.getAttribute("props")))
                        : {}
                    } catch (r) {
                      let s = this.getAttribute("component-url") || "<unknown>",
                        v = this.getAttribute("component-export")
                      throw (
                        (v && (s += ` (export ${v})`),
                        console.error(
                          `[hydrate] Error parsing props for component ${s}`,
                          this.getAttribute("props"),
                          r
                        ),
                        r)
                      )
                    }
                    let u
                    await this.hydrator(this)(this.Component, p, n, {
                      client: this.getAttribute("client"),
                    }),
                      this.removeAttribute("ssr"),
                      this.dispatchEvent(new CustomEvent("astro:hydrate"))
                  })
                  d(this, "unmount", () => {
                    this.isConnected ||
                      this.dispatchEvent(new CustomEvent("astro:unmount"))
                  })
                }
                disconnectedCallback() {
                  document.removeEventListener("astro:after-swap", this.unmount),
                    document.addEventListener("astro:after-swap", this.unmount, {
                      once: !0,
                    })
                }
                connectedCallback() {
                  if (
                    !this.hasAttribute("await-children") ||
                    document.readyState === "interactive" ||
                    document.readyState === "complete"
                  )
                    this.childrenConnectedCallback()
                  else {
                    let e = () => {
                        document.removeEventListener("DOMContentLoaded", e),
                          c.disconnect(),
                          this.childrenConnectedCallback()
                      },
                      c = new MutationObserver(() => {
                        var n
                        ;((n = this.lastChild) == null ? void 0 : n.nodeType) ===
                          Node.COMMENT_NODE &&
                          this.lastChild.nodeValue === "astro:end" &&
                          (this.lastChild.remove(), e())
                      })
                    c.observe(this, { childList: !0 }),
                      document.addEventListener("DOMContentLoaded", e)
                  }
                }
                async childrenConnectedCallback() {
                  let e = this.getAttribute("before-hydration-url")
                  e && (await import(e)), this.start()
                }
                async start() {
                  let e = JSON.parse(this.getAttribute("opts")),
                    c = this.getAttribute("client")
                  if (Astro[c] === void 0) {
                    window.addEventListener(`astro:${c}`, () => this.start(), {
                      once: !0,
                    })
                    return
                  }
                  try {
                    await Astro[c](
                      async () => {
                        let n = this.getAttribute("renderer-url"),
                          [h, { default: p }] = await Promise.all([
                            import(this.getAttribute("component-url")),
                            n ? import(n) : () => () => {},
                          ]),
                          u = this.getAttribute("component-export") || "default"
                        if (!u.includes(".")) this.Component = h[u]
                        else {
                          this.Component = h
                          for (let f of u.split(".")) this.Component = this.Component[f]
                        }
                        return (this.hydrator = p), this.hydrate
                      },
                      e,
                      this
                    )
                  } catch (n) {
                    console.error(
                      `[astro-island] Error hydrating ${this.getAttribute(
                        "component-url"
                      )}`,
                      n
                    )
                  }
                }
                attributeChangedCallback() {
                  this.hydrate()
                }
              }
              d(y, "observedAttributes", ["props"]),
                customElements.get("astro-island") ||
                  customElements.define("astro-island", y)
            }
          })()
        </script>
        <astro-island
          uid="ZUlmm"
          component-url="/_astro/lastfm-bg.BicuCuOI.js"
          component-export="LastFmBackground"
          renderer-url="/_astro/client.C68-onsO.js"
          props='{"useDefaultImages":[0,true]}'
          client="only"
          opts='{"name":"LastFmBackground","value":true}'
          ><div class="cross-fade-container css-17b5exg">
            <div class="css-17w21ee css-68j6hg"></div>
            <div class="css-17w21ee css-7be0f7">
              <div
                style="
                  position: absolute;
                  inset: 0px;
                  pointer-events: none;
                  opacity: 0.5;
                  transition: none;
                "
              >
                <div
                  class="fixed inset-0 -z-10 h-screen w-full max-w-screen overflow-hidden transition-colors duration-300 dark:bg-neutral-900"
                >
                  <div
                    class="absolute inset-0"
                    style="
                      position: relative;
                      width: 100%;
                      height: 100%;
                      overflow: hidden;
                      pointer-events: auto;
                      transition: opacity 600ms ease-out;
                      opacity: 0.5;
                      will-change: opacity;
                      transform: translateZ(0px);
                    "
                  >
                    <div style="width: 100%; height: 100%">
                      <canvas
                        style="display: block; width: 1596px; height: 815px"
                        data-engine="three.js r178"
                        width="1596"
                        height="815"
                      ></canvas>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <svg class="pointer-events-none absolute h-24 w-24" aria-hidden="true">
            <defs>
              <filter id="grain-static">
                <feTurbulence
                  type="fractalNoise"
                  baseFrequency="0.45"
                  numOctaves="4"
                  seed="5"
                ></feTurbulence>
                <feComponentTransfer>
                  <feFuncR type="table" tableValues="0.06666666666666667 0"></feFuncR>
                  <feFuncG type="table" tableValues="0.2 0.06666666666666667"></feFuncG>
                  <feFuncB type="table" tableValues="0.2 0.06666666666666667"></feFuncB>
                  <feFuncA
                    type="discrete"
                    tableValues="0 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 1"
                  ></feFuncA>
                </feComponentTransfer>
              </filter>
              <filter id="grain-animated">
                <feTurbulence
                  type="fractalNoise"
                  baseFrequency="0.45"
                  numOctaves="4"
                  seed="5"
                >
                  <animate
                    attributeName="seed"
                    values="0;1;2;3;4;5;6;7;8;9;10"
                    dur="0.8s"
                    repeatCount="indefinite"
                  ></animate>
                </feTurbulence>
                <feComponentTransfer>
                  <feFuncR type="table" tableValues="0.06666666666666667 0"></feFuncR>
                  <feFuncG type="table" tableValues="0.2 0.06666666666666667"></feFuncG>
                  <feFuncB type="table" tableValues="0.2 0.06666666666666667"></feFuncB>
                  <feFuncA
                    type="discrete"
                    tableValues="0 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 .5 1"
                  ></feFuncA>
                </feComponentTransfer>
              </filter>
            </defs>
          </svg>
          <div
            class="pointer-events-none fixed inset-0"
            style="
              opacity: 0.15;
              mix-blend-mode: screen;
              filter: url('#grain-animated');
              will-change: filter;
            "
          ></div
        ></astro-island>
      </div>
      <main class="flex w-full flex-col items-start space-y-4">
        <div class="space-y-2 text-start">
          <img
            src="/natb.jpg"
            alt="natalie's profile"
            class="ambilight h-32 w-32 rounded-full border border-neutral-500/50"
          />
          <div>
            <h2 class="text-3xl font-bold">natalie b.</h2>
            <p class="text-xl text-gray-800 dark:text-gray-400">
              software, design, music. - nashville, tn.
            </p>
          </div>
          <div class="flex py-2" style="font-size: 1.75rem">
            <div class="pr-[.25em] text-gray-700 dark:text-gray-400">
              <a href="https://github.com/espeon"
                ><svg
                  stroke="currentColor"
                  fill="currentColor"
                  stroke-width="0"
                  viewBox="0 0 496 512"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"
                  ></path></svg
              ></a>
            </div>
            <div class="pr-[.25em] text-gray-700 dark:text-gray-400">
              <a href="https://bsky.app/profile/natalie.sh"
                ><svg
                  stroke="currentColor"
                  fill="currentColor"
                  stroke-width="0"
                  viewBox="0 0 576 512"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M407.8 294.7c-3.3-.4-6.7-.8-10-1.3c3.4 .4 6.7 .9 10 1.3zM288 227.1C261.9 176.4 190.9 81.9 124.9 35.3C61.6-9.4 37.5-1.7 21.6 5.5C3.3 13.8 0 41.9 0 58.4S9.1 194 15 213.9c19.5 65.7 89.1 87.9 153.2 80.7c3.3-.5 6.6-.9 10-1.4c-3.3 .5-6.6 1-10 1.4C74.3 308.6-9.1 342.8 100.3 464.5C220.6 589.1 265.1 437.8 288 361.1c22.9 76.7 49.2 222.5 185.6 103.4c102.4-103.4 28.1-156-65.8-169.9c-3.3-.4-6.7-.8-10-1.3c3.4 .4 6.7 .9 10 1.3c64.1 7.1 133.6-15.1 153.2-80.7C566.9 194 576 75 576 58.4s-3.3-44.7-21.6-52.9c-15.8-7.1-40-14.9-103.2 29.8C385.1 81.9 314.1 176.4 288 227.1z"
                  ></path></svg
              ></a>
            </div>
            <div class="pr-[.25em] text-gray-700 dark:text-gray-400">
              <a href="https://twitch.tv/uxieq"
                ><svg
                  stroke="currentColor"
                  fill="currentColor"
                  stroke-width="0"
                  viewBox="0 0 512 512"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M80 32l-32 80v304h96v64h64l64-64h80l112-112V32H80zm336 256l-64 64h-96.001L192 416v-64h-80V80h304v208z"
                  ></path>
                  <path d="M320 143h48v129h-48zM208 143h48v129h-48z"></path></svg
              ></a>
            </div>
            <div class="pr-[.25em] text-gray-700 dark:text-gray-400">
              <a href="https://last.fm/user/kanb"
                ><svg
                  stroke="currentColor"
                  fill="currentColor"
                  stroke-width="0"
                  viewBox="0 0 512 512"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M225.8 367.1l-18.8-51s-30.5 34-76.2 34c-40.5 0-69.2-35.2-69.2-91.5 0-72.1 36.4-97.9 72.1-97.9 66.5 0 74.8 53.3 100.9 134.9 18.8 56.9 54 102.6 155.4 102.6 72.7 0 122-22.3 122-80.9 0-72.9-62.7-80.6-115-92.1-25.8-5.9-33.4-16.4-33.4-34 0-19.9 15.8-31.7 41.6-31.7 28.2 0 43.4 10.6 45.7 35.8l58.6-7c-4.7-52.8-41.1-74.5-100.9-74.5-52.8 0-104.4 19.9-104.4 83.9 0 39.9 19.4 65.1 68 76.8 44.9 10.6 79.8 13.8 79.8 45.7 0 21.7-21.1 30.5-61 30.5-59.2 0-83.9-31.1-97.9-73.9-32-96.8-43.6-163-161.3-163C45.7 113.8 0 168.3 0 261c0 89.1 45.7 137.2 127.9 137.2 66.2 0 97.9-31.1 97.9-31.1z"
                  ></path></svg
              ></a>
            </div>
            <div class="pr-[.25em] text-gray-700 dark:text-gray-400">
              <a href="https://discord.gg/pgGM9n8ppf"
                ><svg
                  stroke="currentColor"
                  fill="currentColor"
                  stroke-width="0"
                  viewBox="0 0 640 512"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z"
                  ></path></svg
              ></a>
            </div>
          </div>
          <div class="prose dark:prose-invert mb-0 max-w-lg space-y-2">
            <div class="font-semibold">
              i’m a full-stack developer, interested primarily in the web and audiovisual
              media.
            </div>
            <div>
              most recently, i've been working at
              <a
                class="underline dark:border-sky-200 dark:text-sky-200"
                href="https://stream.place/"
                >streamplace</a
              >, helping make live video easy and open for everyone, forever.
            </div>
            <div>
              together with
              <a
                class="underline dark:border-sky-200 dark:text-sky-200"
                href="https://mmatt.net"
                >matt</a
              >, i founded
              <a
                class="underline dark:border-sky-200 dark:text-sky-200"
                href="https://teal.fm"
                >teal.fm</a
              >
              which is a platform for beautifully tracking your music. it should be out
              shortly. i've also created
              <a
                href="https://github.com/espeon/muse"
                class="underline dark:border-sky-200 dark:text-sky-200"
                >muse</a
              >, a local music streaming platform close to my heart.
            </div>
            <div>
              when i'm not at work, you'll find me exploring new music, tinkering with
              audio gear, or reading. always happy to chat about web tech, music
              production, or whatever project is currently consuming my brain.
            </div>
          </div>
        </div>
        <div class="h-26 max-w-full border-t border-neutral-500/50 pt-6">
          <astro-island
            uid="Z1hcKNt"
            component-url="/_astro/lastfm.CbHHvkWq.js"
            component-export="LastFM"
            renderer-url="/_astro/client.C68-onsO.js"
            props="{}"
            client="only"
            opts='{"name":"LastFM","value":true}'
            ><div class="cross-fade-container css-17b5exg">
              <div class="css-1hn7lg9 css-68j6hg"></div>
              <div class="css-1hn7lg9 css-7be0f7">
                <div
                  class="justify-left group flex h-full w-[95vw] max-w-screen min-w-full flex-row items-center overflow-visible xl:max-w-lg"
                >
                  <div class="h-20 overflow-visible">
                    <div class="relative">
                      <img
                        alt="cover"
                        class="transition-opacity duration-300 margin-auto ambilight z-20 mr-4 max-h-20 max-w-20 self-center overflow-visible rounded-lg border border-gray-500/20 contain-content dark:border-neutral-600/30 opacity-100"
                        src="https://lastfm.freetls.fastly.net/i/u/300x300/********************************.jpg"
                      />
                    </div>
                  </div>
                  <div
                    class="items-left flex w-min max-w-[calc(95%-6rem)] flex-col justify-center leading-normal"
                  >
                    <div class="flex max-w-screen justify-between lg:min-w-sm">
                      <div
                        class="w-max text-left text-sm text-gray-600 dark:text-gray-400"
                      >
                        Last Played on
                        <a href="https://www.last.fm/user/kanb" target="_blank"
                          ><svg
                            stroke="currentColor"
                            fill="currentColor"
                            stroke-width="0"
                            viewBox="0 0 512 512"
                            class="hover:text-wisteria-500 dark:hover:text-wisteria-200 mb-0.5 inline text-base transition-colors duration-150"
                            height="1em"
                            width="1em"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M225.8 367.1l-18.8-51s-30.5 34-76.2 34c-40.5 0-69.2-35.2-69.2-91.5 0-72.1 36.4-97.9 72.1-97.9 66.5 0 74.8 53.3 100.9 134.9 18.8 56.9 54 102.6 155.4 102.6 72.7 0 122-22.3 122-80.9 0-72.9-62.7-80.6-115-92.1-25.8-5.9-33.4-16.4-33.4-34 0-19.9 15.8-31.7 41.6-31.7 28.2 0 43.4 10.6 45.7 35.8l58.6-7c-4.7-52.8-41.1-74.5-100.9-74.5-52.8 0-104.4 19.9-104.4 83.9 0 39.9 19.4 65.1 68 76.8 44.9 10.6 79.8 13.8 79.8 45.7 0 21.7-21.1 30.5-61 30.5-59.2 0-83.9-31.1-97.9-73.9-32-96.8-43.6-163-161.3-163C45.7 113.8 0 168.3 0 261c0 89.1 45.7 137.2 127.9 137.2 66.2 0 97.9-31.1 97.9-31.1z"
                            ></path></svg
                        ></a>
                      </div>
                      <a
                        href="/lfm"
                        class="opacity-0 duration-150 group-hover:opacity-100"
                        ><svg
                          stroke="currentColor"
                          fill="none"
                          stroke-width="2"
                          viewBox="0 0 24 24"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          height="1em"
                          width="1em"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M15 3h6v6"></path>
                          <path d="M10 14 21 3"></path>
                          <path
                            d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"
                          ></path></svg
                      ></a>
                    </div>
                    <a href="https://www.last.fm/music/Susto/_/Get Down" target="_blank"
                      ><div
                        class="hover:text-wisteria-500 dark:hover:text-wisteria-200 transition-colors duration-150 relative w-full overflow-hidden"
                      >
                        <div
                          class="whitespace-nowrap"
                          style="
                            animation-duration: 0s;
                            animation-timing-function: linear;
                            animation-iteration-count: infinite;
                            --text-width: calc(0px + (32px / 2));
                          "
                        >
                          <span>Get Down</span>
                        </div>
                      </div></a
                    ><a href="https://www.last.fm/music/Susto/" target="_blank"
                      ><div
                        class="hover:text-wisteria-500 dark:hover:text-wisteria-200 transition-colors duration-150 relative w-full overflow-hidden"
                      >
                        <div
                          class="whitespace-nowrap"
                          style="
                            animation-duration: 0s;
                            animation-timing-function: linear;
                            animation-iteration-count: infinite;
                            --text-width: calc(0px + (32px / 2));
                          "
                        >
                          <span>Susto</span>
                        </div>
                      </div></a
                    >
                  </div>
                </div>
              </div>
            </div></astro-island
          >
        </div>
      </main>
    </div>
    <div aria-live="assertive" aria-atomic="true" class="astro-route-announcer">
      natalie's blog
    </div>
  </body>
</html>
