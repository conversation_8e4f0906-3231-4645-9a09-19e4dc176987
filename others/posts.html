<html lang="en" class="light">
  <head>
    <!-- Global Metadata -->
    <!-- Font preloads -->
    <link
      href="https://fonts.googleapis.com/css2?family=Atkinson+Hyperlegible+Next:ital,wght@0,200..800;1,200..800&amp;family=Baskervville:ital,wght@0,400..700;1,400..700&amp;display=swap"
      rel="stylesheet"
    />
    <!-- Canonical URL -->
    <!-- Primary Meta Tags -->
    <!-- Open Graph / Facebook -->
    <!-- Twitter -->
    <link rel="stylesheet" href="/_astro/lfm.erUP-9BY.css" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content="Astro v5.13.4" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
    <link
      rel="preload"
      href="/fonts/geist-mono-variable.woff2"
      as="font"
      type="font/woff2"
      crossorigin=""
    />
    <link rel="canonical" href="https://natalie.sh/posts/" />
    <title>natalie's blog</title>
    <meta name="title" content="natalie's blog" />
    <meta
      name="description"
      content="a blog about web development, design, and other things I find interesting"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://natalie.sh/posts/" />
    <meta property="og:title" content="natalie's blog" />
    <meta
      property="og:description"
      content="a blog about web development, design, and other things I find interesting"
    />
    <meta
      property="og:image"
      content="https://ogimage-workers.kanbaru.workers.dev/?title=natalie%27s+blog&amp;liner=a+blog+about+web+development%2C+design%2C+and+other+things+I+find+interesting&amp;date=Aug+28%2C+2025"
    />
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://natalie.sh/posts/" />
    <meta property="twitter:title" content="natalie's blog" />
    <meta
      property="twitter:description"
      content="a blog about web development, design, and other things I find interesting"
    />
    <meta
      property="twitter:image"
      content="https://ogimage-workers.kanbaru.workers.dev/?title=natalie%27s+blog&amp;liner=a+blog+about+web+development%2C+design%2C+and+other+things+I+find+interesting&amp;date=Aug+28%2C+2025"
    />
    <style>
      body {
        visibility: hidden;
        opacity: 0;
      }
    </style>
    <script data-astro-exec="">
      const setTheme = () => {
        let theme

        if (typeof localStorage !== "undefined" && localStorage.getItem("theme")) {
          theme = localStorage.getItem("theme")
        } else {
          theme = window.matchMedia("(prefers-color-scheme: dark)").matches
            ? "dark"
            : "light"
        }

        document.documentElement.classList[theme ? "add" : "remove"](theme)

        if (typeof localStorage !== "undefined") {
          const observer = new MutationObserver(() => {
            const isDark = document.documentElement.classList.contains("dark")
            localStorage.setItem("theme", isDark ? "dark" : "light")
          })
          observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ["class"],
          })
        }
      }

      setTheme()

      document.addEventListener("astro:page-load", () => {
        setTheme()
      })

      document.addEventListener("astro:after-swap", () => {
        setTheme()
      })
    </script>
    <meta name="astro-view-transitions-enabled" content="true" />
    <meta name="astro-view-transitions-fallback" content="animate" />
    <script
      type="module"
      src="/_astro/ClientRouter.astro_astro_type_script_index_0_lang.DZnDNxNb.js"
      data-astro-exec=""
    ></script>
    <style>
      /*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
      @layer properties {
        @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or
          ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
          *,
          :before,
          :after,
          ::backdrop {
            --tw-font-weight: initial;
          }
        }
      }
      a[data-astro-cid-3ef6ksr2] {
        --tw-font-weight: var(--font-weight-medium, 500);
        font-weight: var(--font-weight-medium, 500);
        color: var(--color-zinc-500, oklch(55.2% 0.016 285.938));
      }
      @media (hover: hover) {
        a[data-astro-cid-3ef6ksr2]:hover {
          color: var(--color-zinc-900, oklch(21% 0.006 285.885));
          text-decoration-line: underline;
        }
        a[data-astro-cid-3ef6ksr2]:is(.dark *):hover {
          color: var(--color-zinc-100, oklch(96.7% 0.001 286.375));
        }
      }
      @property --tw-font-weight {
        syntax: "*";
        inherits: false;
      }
      body {
        visibility: visible;
        opacity: 1;
        transition: visibility 0.2s linear, opacity 0.2s linear;
      }
      @keyframes astroFadeInOut {
        0% {
          opacity: 1;
        }
        to {
          opacity: 0;
        }
      }
      @keyframes astroFadeIn {
        0% {
          opacity: 0;
          mix-blend-mode: plus-lighter;
        }
        to {
          opacity: 1;
          mix-blend-mode: plus-lighter;
        }
      }
      @keyframes astroFadeOut {
        0% {
          opacity: 1;
          mix-blend-mode: plus-lighter;
        }
        to {
          opacity: 0;
          mix-blend-mode: plus-lighter;
        }
      }
      @keyframes astroSlideFromRight {
        0% {
          transform: translate(100%);
        }
      }
      @keyframes astroSlideFromLeft {
        0% {
          transform: translate(-100%);
        }
      }
      @keyframes astroSlideToRight {
        to {
          transform: translate(100%);
        }
      }
      @keyframes astroSlideToLeft {
        to {
          transform: translate(-100%);
        }
      }
      @media (prefers-reduced-motion) {
        ::view-transition-group(*),
        ::view-transition-old(*),
        ::view-transition-new(*) {
          animation: none !important;
        }
        [data-astro-transition-scope] {
          animation: none !important;
        }
      }
    </style>
    <style>
      [data-astro-transition-scope="astro-l7r54iwe-1"] {
        view-transition-name: header;
      }
      @layer astro {
        ::view-transition-old(header) {
          animation-duration: 180ms;
          animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
          animation-fill-mode: both;
          animation-name: astroFadeOut;
        }
        ::view-transition-new(header) {
          animation-duration: 180ms;
          animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
          animation-fill-mode: both;
          animation-name: astroFadeIn;
        }
        [data-astro-transition="back"]::view-transition-old(header) {
          animation-duration: 180ms;
          animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
          animation-fill-mode: both;
          animation-name: astroFadeOut;
        }
        [data-astro-transition="back"]::view-transition-new(header) {
          animation-duration: 180ms;
          animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
          animation-fill-mode: both;
          animation-name: astroFadeIn;
        }
      }
      [data-astro-transition-fallback="old"]
        [data-astro-transition-scope="astro-l7r54iwe-1"],
      [data-astro-transition-fallback="old"][data-astro-transition-scope="astro-l7r54iwe-1"] {
        animation-duration: 180ms;
        animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
        animation-fill-mode: both;
        animation-name: astroFadeOut;
      }
      [data-astro-transition-fallback="new"]
        [data-astro-transition-scope="astro-l7r54iwe-1"],
      [data-astro-transition-fallback="new"][data-astro-transition-scope="astro-l7r54iwe-1"] {
        animation-duration: 180ms;
        animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
        animation-fill-mode: both;
        animation-name: astroFadeIn;
      }
      [data-astro-transition="back"][data-astro-transition-fallback="old"]
        [data-astro-transition-scope="astro-l7r54iwe-1"],
      [data-astro-transition="back"][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-l7r54iwe-1"] {
        animation-duration: 180ms;
        animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
        animation-fill-mode: both;
        animation-name: astroFadeOut;
      }
      [data-astro-transition="back"][data-astro-transition-fallback="new"]
        [data-astro-transition-scope="astro-l7r54iwe-1"],
      [data-astro-transition="back"][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-l7r54iwe-1"] {
        animation-duration: 180ms;
        animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
        animation-fill-mode: both;
        animation-name: astroFadeIn;
      }
    </style>
  </head>
  <body
    class="max-h-max min-h-screen w-screen max-w-screen overflow-x-clip font-sans text-zinc-900 antialiased transition-colors dark:bg-zinc-900 dark:text-zinc-200"
    cz-shortcut-listen="true"
  >
    <div
      id="page-container"
      class="mx-auto p-4 layout-transition default-layout max-w-prose"
      style="view-transition-name: page-container"
    >
      <header
        class="mb-6 flex items-center justify-between"
        data-astro-cid-3ef6ksr2=""
        data-astro-transition-scope="astro-l7r54iwe-1"
      >
        <nav
          class="flex items-center gap-4 text-gray-950 dark:text-gray-300"
          data-astro-cid-3ef6ksr2=""
        >
          <a href="/" data-astro-cid-3ef6ksr2=""> home </a>
          <a href="/cv" data-astro-cid-3ef6ksr2=""> work </a>
          <a href="/posts" data-astro-cid-3ef6ksr2=""> posts </a>
          <a href="/uses" data-astro-cid-3ef6ksr2=""> uses </a>
        </nav>
        <button
          id="theme-toggle"
          class="inline-flex items-center rounded-lg border p-1.5 transition-colors hover:bg-zinc-50 dark:border-zinc-700 dark:hover:bg-zinc-800 [&amp;_svg]:text-zinc-500 hover:[&amp;_svg]:text-zinc-900 dark:hover:[&amp;_svg]:text-zinc-100"
          aria-label="Toggle theme"
        >
          <span class="sr-only">Toggle theme</span>
          <svg
            id="sun"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 16 16"
            fill="currentColor"
            class="hidden size-4 dark:block"
          >
            <path
              d="M8 1a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 8 1ZM10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM12.95 4.11a.75.75 0 1 0-1.06-1.06l-1.062 1.06a.75.75 0 0 0 1.061 1.062l1.06-1.061ZM15 8a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5A.75.75 0 0 1 15 8ZM11.89 12.95a.75.75 0 0 0 1.06-1.06l-1.06-1.062a.75.75 0 0 0-1.062 1.061l1.061 1.06ZM8 12a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 8 12ZM5.172 11.89a.75.75 0 0 0-1.061-1.062L3.05 11.89a.75.75 0 1 0 1.06 1.06l1.06-1.06ZM4 8a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5A.75.75 0 0 1 4 8ZM4.11 5.172A.75.75 0 0 0 5.173 4.11L4.11 3.05a.75.75 0 1 0-1.06 1.06l1.06 1.06Z"
            ></path>
          </svg>
          <svg
            id="moon"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 16 16"
            fill="currentColor"
            class="size-4 dark:hidden"
          >
            <path
              d="M14.438 10.148c.19-.425-.321-.787-.748-.601A5.5 5.5 0 0 1 6.453 2.31c.186-.427-.176-.938-.6-.748a6.501 6.501 0 1 0 8.585 8.586Z"
            ></path>
          </svg>
        </button>
        <script type="module" data-astro-exec="">
          const t = () => {
            document.getElementById("theme-toggle")?.addEventListener("click", () => {
              document.documentElement.classList.toggle("dark")
            })
          }
          t()
          document.addEventListener("astro:after-swap", t)
        </script>
      </header>
      <main>
        <ul class="flex flex-col gap-1.5">
          <li>
            <a
              href="/posts/bluesky-comments"
              class="group flex justify-between gap-3 pb-3"
            >
              <div>
                <div class="group-hover:underline">
                  Building Bluesky Comments for My Blog
                </div>
                <div class="text-neutral-400">I hate disqus too much.</div>
              </div>
              <span class="text-nowrap text-zinc-500"> August 6, 2025 </span>
            </a>
          </li>
          <li>
            <a href="/posts/setting-up-sp" class="group flex justify-between gap-3 pb-3">
              <div>
                <div class="group-hover:underline">
                  Setting up my own Streamplace server
                </div>
                <div class="text-neutral-400">
                  In and out, quick hour-long adventure, right?
                </div>
              </div>
              <span class="text-nowrap text-zinc-500"> July 15, 2025 </span>
            </a>
          </li>
        </ul>
      </main>
    </div>
    <div aria-live="assertive" aria-atomic="true" class="astro-route-announcer">
      natalie's blog
    </div>
  </body>
</html>
