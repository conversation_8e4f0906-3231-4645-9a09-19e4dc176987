@import "https://fonts.googleapis.com/css2?family=Atkinson+Hyperlegible+Next:ital,wght@0,200..800;1,200..800&family=Baskervville:ital,wght@0,400..700;1,400..700&display=swap";
.astro-route-announcer {
  position: absolute;
  left: 0;
  top: 0;
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  overflow: hidden;
  white-space: nowrap;
  width: 1px;
  height: 1px;
}

/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or
    ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *,
    :before,
    :after,
    ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}

@layer theme {
  :root,
  :host {
    --font-sans: "Atkinson Hyperlegible Next", ui-sans-serif, system-ui, sans-serif,
      "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: "Geist Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
      "Liberation Mono", "Courier New", monospace;
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-900: oklch(39.6% 0.141 25.723);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-sky-200: oklch(90.1% 0.058 230.902);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-pink-500: oklch(65.6% 0.241 354.308);
    --color-pink-600: oklch(59.2% 0.249 0.584);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-gray-950: oklch(13% 0.028 261.692);
    --color-zinc-50: oklch(98.5% 0 0);
    --color-zinc-100: oklch(96.7% 0.001 286.375);
    --color-zinc-200: oklch(92% 0.004 286.32);
    --color-zinc-300: oklch(87.1% 0.006 286.286);
    --color-zinc-400: oklch(70.5% 0.015 286.067);
    --color-zinc-500: oklch(55.2% 0.016 285.938);
    --color-zinc-600: oklch(44.2% 0.017 285.786);
    --color-zinc-700: oklch(37% 0.013 285.805);
    --color-zinc-800: oklch(27.4% 0.006 286.033);
    --color-zinc-900: oklch(21% 0.006 285.885);
    --color-neutral-200: oklch(92.2% 0 0);
    --color-neutral-300: oklch(87% 0 0);
    --color-neutral-400: oklch(70.8% 0 0);
    --color-neutral-500: oklch(55.6% 0 0);
    --color-neutral-600: oklch(43.9% 0 0);
    --color-neutral-800: oklch(26.9% 0 0);
    --color-neutral-900: oklch(20.5% 0 0);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-sm: 24rem;
    --container-lg: 32rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: 1.5;
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: 1.2;
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-black: 900;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --radius-sm: 0.25rem;
    --radius-lg: 0.5rem;
    --radius-2xl: 1rem;
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --blur-sm: 8px;
    --default-transition-duration: 0.15s;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}

@layer base {
  *,
  :after,
  :before,
  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html,: host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(
      --default-font-family,
      ui-sans-serif,
      system-ui,
      sans-serif,
      "Apple Color Emoji",
      "Segoe UI Emoji",
      "Segoe UI Symbol",
      "Noto Color Emoji"
    );
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr: where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b,
  strong {
    font-weight: bolder;
  }

  code,
  kbd,
  samp,
  pre {
    font-family: var(
      --default-mono-font-family,
      ui-monospace,
      SFMono-Regular,
      Menlo,
      Monaco,
      Consolas,
      "Liberation Mono",
      "Courier New",
      monospace
    );
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub,
  sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -0.25em;
  }

  sup {
    top: -0.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol,
  ul,
  menu {
    list-style: none;
  }

  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    vertical-align: middle;
    display: block;
  }

  img,
  video {
    max-width: 100%;
    height: auto;
  }

  button,
  input,
  select,
  optgroup,
  textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select: is([multiple],[size])) optgroup {
    font-weight: bolder;
  }

  :where(select: is([multiple],[size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or
    (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button,input: where([type=button],[type=reset],[type=submit]) {
    appearance: button;
  }

  ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]: where(:not([hidden=until-found])) {
    display: none !important;
  }

  *,
  :after,
  :before,
  ::backdrop {
    border-color: var(--color-gray-200, currentcolor);
  }

  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }

  @font-face {
    font-family: Geist Mono;
    src: url(/fonts/geist-mono-variable.woff2) format("woff2");
    font-display: swap;
  }

  html.dark .astro-code,
  html.dark .astro-code span {
    color: var(--shiki-dark) !important;
    background-color: var(--shiki-dark-bg) !important;
    font-style: var(--shiki-dark-font-style) !important;
    font-weight: var(--shiki-dark-font-weight) !important;
    -webkit-text-decoration: var(--shiki-dark-text-decoration) !important;
    text-decoration: var(--shiki-dark-text-decoration) !important;
  }
}

@layer components {
  article p {
    margin-block: calc(var(--spacing) * 4);
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  article h1 {
    margin-block: calc(var(--spacing) * 4);
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  article h1: is(.dark *) {
    color: var(--color-white);
  }

  article h2 {
    margin-block: calc(var(--spacing) * 4);
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  article h2: is(.dark *) {
    color: var(--color-white);
  }

  article h3,
  article h4,
  article h5,
  article h6 {
    margin-block: calc(var(--spacing) * 4);
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  :is(article h3,article h4,article h5,article h6): is(.dark *) {
    color: var(--color-white);
  }

  article : not(pre)>code {
    border-radius: var(--radius-sm);
    border-style: var(--tw-border-style);
    background-color: #e4e4e780;
    border-width: 1px;
  }

  @supports (color: color-mix(in lab, red, red)) {
    article :not(pre) > code {
      background-color: color-mix(in oklab, var(--color-zinc-200) 50%, transparent);
    }
  }

  article : not(pre)>code {
    padding-inline: calc(var(--spacing) * 1);
    padding-block: calc(var(--spacing) * 0.5);
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    color: var(--color-black);
  }

  article : not(pre)>code:is(.dark *) {
    border-color: var(--color-zinc-700);
    background-color: #27272a80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    article :not(pre) > code:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-zinc-800) 50%, transparent);
    }
  }

  article : not(pre)>code:is(.dark *) {
    color: var(--color-white);
  }

  article pre: has(code) {
    margin-block: calc(var(--spacing) * 4);
    border-radius: var(--radius-lg);
    border-style: var(--tw-border-style);
    max-height: 600px;
    padding: calc(var(--spacing) * 4);
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    border-width: 1px;
    overflow: auto;
  }

  article pre: has(code):is(.dark *) {
    border-color: var(--color-zinc-700);
  }

  article img {
    margin-block: calc(var(--spacing) * 4);
    border-radius: var(--radius-lg);
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  article img: is(.dark *) {
    border-color: var(--color-zinc-700);
  }

  article video {
    margin-block: calc(var(--spacing) * 4);
    border-radius: var(--radius-lg);
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  article video: is(.dark *) {
    border-color: var(--color-zinc-700);
  }

  article blockquote {
    margin-block: calc(var(--spacing) * 4);
    border-left-style: var(--tw-border-style);
    padding-left: calc(var(--spacing) * 4);
    border-left-width: 2px;
  }

  article a {
    color: var(--color-blue-500);
    text-decoration-line: underline;
  }

  article table {
    margin-block: calc(var(--spacing) * 4);
    table-layout: auto;
    border-collapse: collapse;
    width: 100%;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  article table th {
    border-bottom-style: var(--tw-border-style);
    padding: calc(var(--spacing) * 4);
    text-align: left;
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    border-bottom-width: 1px;
  }

  article table th: is(.dark *) {
    border-color: var(--color-zinc-700);
  }

  article table td {
    border-bottom-style: var(--tw-border-style);
    background-color: var(--color-zinc-50);
    padding: calc(var(--spacing) * 4);
    border-bottom-width: 1px;
  }

  article table td: is(.dark *) {
    border-color: var(--color-zinc-700);
    background-color: var(--color-zinc-800);
  }

  article ol {
    margin-block: calc(var(--spacing) * 1);
    list-style-type: decimal;
  }

  :where(article ol>: not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(
      calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse))
    );
  }

  article ol ol,
  article ol ul {
    padding-left: calc(var(--spacing) * 5);
  }

  article ul {
    margin-block: calc(var(--spacing) * 1);
    list-style-type: disc;
  }

  :where(article ul>: not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(
      calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse))
    );
  }

  article ul ol,
  article ul ul {
    padding-left: calc(var(--spacing) * 5);
  }

  article kbd {
    border-radius: var(--radius-sm);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-bottom-style: var(--tw-border-style);
    background-color: var(--color-zinc-100);
    padding-inline: calc(var(--spacing) * 1);
    padding-block: calc(var(--spacing) * 0.5);
    font-family: var(--font-mono);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-black);
    border-bottom-width: 2px;
  }

  article kbd: is(.dark *) {
    border-color: var(--color-zinc-700);
    background-color: var(--color-zinc-800);
    color: var(--color-white);
  }

  article mark: is(.dark *) {
    background-color: #edb20080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    article mark:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-500) 50%, transparent);
    }
  }

  article mark: is(.dark *) {
    color: var(--color-white);
  }
}

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .-right-4 {
    right: calc(var(--spacing) * -4);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .-left-1 {
    left: calc(var(--spacing) * -1);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .-z-10 {
    z-index: -10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .col-span-2 {
    grid-column: span 2 / span 2;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .mx-auto {
    margin-inline: auto;
  }

  .prose {
    color: var(--tw-prose-body);
    max-width: 65ch;
  }

  .prose :where(p): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose :where([class~=lead]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-lead);
    margin-top: 1.2em;
    margin-bottom: 1.2em;
    font-size: 1.25em;
    line-height: 1.6;
  }

  .prose :where(a): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-links);
    font-weight: 500;
    text-decoration: underline;
  }

  .prose :where(strong): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-bold);
    font-weight: 600;
  }

  .prose :where(a strong): not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(blockquote strong):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(thead th strong):not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: inherit;
  }

  .prose :where(ol): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    padding-inline-start: 1.625em;
    list-style-type: decimal;
  }

  .prose :where(ol[type=A]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    list-style-type: upper-alpha;
  }

  .prose :where(ol[type=a]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    list-style-type: lower-alpha;
  }

  .prose :where(ol[type=A s]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    list-style-type: upper-alpha;
  }

  .prose :where(ol[type=a s]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    list-style-type: lower-alpha;
  }

  .prose :where(ol[type=I]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    list-style-type: upper-roman;
  }

  .prose :where(ol[type=i]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    list-style-type: lower-roman;
  }

  .prose :where(ol[type=I s]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    list-style-type: upper-roman;
  }

  .prose :where(ol[type=i s]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    list-style-type: lower-roman;
  }

  .prose :where(ol[type="1"]): not(:where([class~=not-prose],[class~=not-prose] *)) {
    list-style-type: decimal;
  }

  .prose :where(ul): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    padding-inline-start: 1.625em;
    list-style-type: disc;
  }

  .prose :where(ol>li): not(:where([class~=not-prose],[class~=not-prose] *))::marker {
    color: var(--tw-prose-counters);
    font-weight: 400;
  }

  .prose :where(ul>li): not(:where([class~=not-prose],[class~=not-prose] *))::marker {
    color: var(--tw-prose-bullets);
  }

  .prose :where(dt): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.25em;
    font-weight: 600;
  }

  .prose :where(hr): not(:where([class~=not-prose],[class~=not-prose] *)) {
    border-color: var(--tw-prose-hr);
    border-top-width: 1px;
    margin-top: 3em;
    margin-bottom: 3em;
  }

  .prose :where(blockquote): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-quotes);
    border-inline-start-width: 0.25rem;
    border-inline-start-color: var(--tw-prose-quote-borders);
    quotes: "“" "”" "‘" "’";
    margin-top: 1.6em;
    margin-bottom: 1.6em;
    padding-inline-start: 1em;
    font-style: italic;
    font-weight: 500;
  }

  .prose :where(blockquote p: first-of-type):not(:where([class~=not-prose],[class~=not-prose] *)):before {
    content: open-quote;
  }

  .prose :where(blockquote p: last-of-type):not(:where([class~=not-prose],[class~=not-prose] *)):after {
    content: close-quote;
  }

  .prose :where(h1): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    margin-top: 0;
    margin-bottom: 0.888889em;
    font-size: 2.25em;
    font-weight: 800;
    line-height: 1.11111;
  }

  .prose :where(h1 strong): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: inherit;
    font-weight: 900;
  }

  .prose :where(h2): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    margin-top: 2em;
    margin-bottom: 1em;
    font-size: 1.5em;
    font-weight: 700;
    line-height: 1.33333;
  }

  .prose :where(h2 strong): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: inherit;
    font-weight: 800;
  }

  .prose :where(h3): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.6em;
    margin-bottom: 0.6em;
    font-size: 1.25em;
    font-weight: 600;
    line-height: 1.6;
  }

  .prose :where(h3 strong): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: inherit;
    font-weight: 700;
  }

  .prose :where(h4): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
    line-height: 1.5;
  }

  .prose :where(h4 strong): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: inherit;
    font-weight: 700;
  }

  .prose :where(img): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(picture): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
    display: block;
  }

  .prose :where(video): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(kbd): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-kbd);
    box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%),
      0 3px rgb(var(--tw-prose-kbd-shadows) / 10%);
    padding-top: 0.1875em;
    padding-inline-end: 0.375em;
    padding-bottom: 0.1875em;
    border-radius: 0.3125rem;
    padding-inline-start: 0.375em;
    font-family: inherit;
    font-size: 0.875em;
    font-weight: 500;
  }

  .prose :where(code): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-code);
    font-size: 0.875em;
    font-weight: 600;
  }

  .prose :where(code): not(:where([class~=not-prose],[class~=not-prose] *)):before,.prose :where(code):not(:where([class~=not-prose],[class~=not-prose] *)):after {
    content: "`";
  }

  .prose :where(a code): not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h1 code):not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: inherit;
  }

  .prose :where(h2 code): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: inherit;
    font-size: 0.875em;
  }

  .prose :where(h3 code): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: inherit;
    font-size: 0.9em;
  }

  .prose :where(h4 code): not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(blockquote code):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(thead th code):not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: inherit;
  }

  .prose :where(pre): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-pre-code);
    background-color: var(--tw-prose-pre-bg);
    padding-top: 0.857143em;
    padding-inline-end: 1.14286em;
    padding-bottom: 0.857143em;
    border-radius: 0.375rem;
    margin-top: 1.71429em;
    margin-bottom: 1.71429em;
    padding-inline-start: 1.14286em;
    font-size: 0.875em;
    font-weight: 400;
    line-height: 1.71429;
    overflow-x: auto;
  }

  .prose :where(pre code): not(:where([class~=not-prose],[class~=not-prose] *)) {
    font-weight: inherit;
    color: inherit;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    background-color: #0000;
    border-width: 0;
    border-radius: 0;
    padding: 0;
  }

  .prose :where(pre code): not(:where([class~=not-prose],[class~=not-prose] *)):before,.prose :where(pre code):not(:where([class~=not-prose],[class~=not-prose] *)):after {
    content: none;
  }

  .prose :where(table): not(:where([class~=not-prose],[class~=not-prose] *)) {
    table-layout: auto;
    width: 100%;
    margin-top: 2em;
    margin-bottom: 2em;
    font-size: 0.875em;
    line-height: 1.71429;
  }

  .prose :where(thead): not(:where([class~=not-prose],[class~=not-prose] *)) {
    border-bottom-width: 1px;
    border-bottom-color: var(--tw-prose-th-borders);
  }

  .prose :where(thead th): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    vertical-align: bottom;
    padding-inline-end: 0.571429em;
    padding-bottom: 0.571429em;
    padding-inline-start: 0.571429em;
    font-weight: 600;
  }

  .prose :where(tbody tr): not(:where([class~=not-prose],[class~=not-prose] *)) {
    border-bottom-width: 1px;
    border-bottom-color: var(--tw-prose-td-borders);
  }

  .prose :where(tbody tr: last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
    border-bottom-width: 0;
  }

  .prose :where(tbody td): not(:where([class~=not-prose],[class~=not-prose] *)) {
    vertical-align: baseline;
  }

  .prose :where(tfoot): not(:where([class~=not-prose],[class~=not-prose] *)) {
    border-top-width: 1px;
    border-top-color: var(--tw-prose-th-borders);
  }

  .prose :where(tfoot td): not(:where([class~=not-prose],[class~=not-prose] *)) {
    vertical-align: top;
  }

  .prose :where(th,td): not(:where([class~=not-prose],[class~=not-prose] *)) {
    text-align: start;
  }

  .prose :where(figure>*): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose :where(figcaption): not(:where([class~=not-prose],[class~=not-prose] *)) {
    color: var(--tw-prose-captions);
    margin-top: 0.857143em;
    font-size: 0.875em;
    line-height: 1.42857;
  }

  .prose {
    --tw-prose-body: oklch(37.3% 0.034 259.733);
    --tw-prose-headings: oklch(21% 0.034 264.665);
    --tw-prose-lead: oklch(44.6% 0.03 256.802);
    --tw-prose-links: oklch(21% 0.034 264.665);
    --tw-prose-bold: oklch(21% 0.034 264.665);
    --tw-prose-counters: oklch(55.1% 0.027 264.364);
    --tw-prose-bullets: oklch(87.2% 0.01 258.338);
    --tw-prose-hr: oklch(92.8% 0.006 264.531);
    --tw-prose-quotes: oklch(21% 0.034 264.665);
    --tw-prose-quote-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-captions: oklch(55.1% 0.027 264.364);
    --tw-prose-kbd: oklch(21% 0.034 264.665);
    --tw-prose-kbd-shadows: NaN NaN NaN;
    --tw-prose-code: oklch(21% 0.034 264.665);
    --tw-prose-pre-code: oklch(92.8% 0.006 264.531);
    --tw-prose-pre-bg: oklch(27.8% 0.033 256.848);
    --tw-prose-th-borders: oklch(87.2% 0.01 258.338);
    --tw-prose-td-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-invert-body: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-bullets: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-hr: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-quotes: oklch(96.7% 0.003 264.542);
    --tw-prose-invert-quote-borders: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-captions: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-pre-bg: #00000080;
    --tw-prose-invert-th-borders: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-td-borders: oklch(37.3% 0.034 259.733);
    font-size: 1rem;
    line-height: 1.75;
  }

  .prose :where(picture>img): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose :where(li): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .prose :where(ol>li): not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *)) {
    padding-inline-start: 0.375em;
  }

  .prose :where(.prose>ul>li p): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 0.75em;
    margin-bottom: 0.75em;
  }

  .prose :where(.prose>ul>li>p: first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 1.25em;
  }

  .prose :where(.prose>ul>li>p: last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-bottom: 1.25em;
  }

  .prose :where(.prose>ol>li>p: first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 1.25em;
  }

  .prose :where(.prose>ol>li>p: last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-bottom: 1.25em;
  }

  .prose :where(ul ul,ul ol,ol ul,ol ol): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 0.75em;
    margin-bottom: 0.75em;
  }

  .prose :where(dl): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose :where(dd): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 0.5em;
    padding-inline-start: 1.625em;
  }

  .prose :where(hr+*): not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h2+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h3+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h4+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 0;
  }

  .prose :where(thead th: first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
    padding-inline-start: 0;
  }

  .prose :where(thead th: last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
    padding-inline-end: 0;
  }

  .prose :where(tbody td,tfoot td): not(:where([class~=not-prose],[class~=not-prose] *)) {
    padding-top: 0.571429em;
    padding-inline-end: 0.571429em;
    padding-bottom: 0.571429em;
    padding-inline-start: 0.571429em;
  }

  .prose :where(tbody td: first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
    padding-inline-start: 0;
  }

  .prose :where(tbody td: last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
    padding-inline-end: 0;
  }

  .prose :where(figure): not(:where([class~=not-prose],[class~=not-prose] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose
    :where(.prose > :first-child):not(
      :where([class~="not-prose"], [class~="not-prose"] *)
    ) {
    margin-top: 0;
  }

  .prose
    :where(.prose > :last-child):not(
      :where([class~="not-prose"], [class~="not-prose"] *)
    ) {
    margin-bottom: 0;
  }

  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }

  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .mb-0\.5 {
    margin-bottom: calc(var(--spacing) * 0.5);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-\[20vh\] {
    margin-bottom: 20vh;
  }

  .ml-1\.5 {
    margin-left: calc(var(--spacing) * 1.5);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-26 {
    height: calc(var(--spacing) * 26);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-80 {
    height: calc(var(--spacing) * 80);
  }

  .h-full {
    height: 100%;
  }

  .h-min {
    height: min-content;
  }

  .h-screen {
    height: 100vh;
  }

  .max-h-0 {
    max-height: calc(var(--spacing) * 0);
  }

  .max-h-20 {
    max-height: calc(var(--spacing) * 20);
  }

  .max-h-64 {
    max-height: calc(var(--spacing) * 64);
  }

  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }

  .max-h-\[90vh\] {
    max-height: 90vh;
  }

  .max-h-max {
    max-height: max-content;
  }

  .max-h-screen {
    max-height: 100vh;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-\[95vw\] {
    width: 95vw;
  }

  .w-full {
    width: 100%;
  }

  .w-max {
    width: max-content;
  }

  .w-min {
    width: min-content;
  }

  .w-screen {
    width: 100vw;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-20 {
    max-width: calc(var(--spacing) * 20);
  }

  .max-w-64 {
    max-width: calc(var(--spacing) * 64);
  }

  .max-w-96 {
    max-width: calc(var(--spacing) * 96);
  }

  .max-w-\[90vw\] {
    max-width: 90vw;
  }

  .max-w-\[calc\(95\%-6rem\)\] {
    max-width: calc(95% - 6rem);
  }

  .max-w-\[calc\(95\%-8rem\)\] {
    max-width: calc(95% - 8rem);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-prose {
    max-width: 65ch;
  }

  .max-w-screen {
    max-width: 100vw;
  }

  .min-w-full {
    min-width: 100%;
  }

  .shrink {
    flex-shrink: 1;
  }

  .grow {
    flex-grow: 1;
  }

  .-translate-y-full {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .rotate-0 {
    rotate: none;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x)
      var(--tw-skew-y);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize {
    resize: both;
  }

  .list-outside {
    list-style-position: outside;
  }

  .list-disc {
    list-style-type: disc;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-row {
    flex-direction: row;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(
      calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse))
    );
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(
      calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse))
    );
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(
      calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse))
    );
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(
      calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse))
    );
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(
      calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse))
    );
  }

  .self-center {
    align-self: center;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-visible {
    overflow: visible;
  }

  .overflow-x-clip {
    overflow-x: clip;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: 0.25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-300\/20 {
    border-color: #d1d5dc33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-300\/20 {
      border-color: color-mix(in oklab, var(--color-gray-300) 20%, transparent);
    }
  }

  .border-gray-300\/30 {
    border-color: #d1d5dc4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-300\/30 {
      border-color: color-mix(in oklab, var(--color-gray-300) 30%, transparent);
    }
  }

  .border-gray-500\/20 {
    border-color: #6a728233;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-500\/20 {
      border-color: color-mix(in oklab, var(--color-gray-500) 20%, transparent);
    }
  }

  .border-neutral-200 {
    border-color: var(--color-neutral-200);
  }

  .border-neutral-400\/50 {
    border-color: #a1a1a180;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-400\/50 {
      border-color: color-mix(in oklab, var(--color-neutral-400) 50%, transparent);
    }
  }

  .border-neutral-500\/50 {
    border-color: #73737380;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-500\/50 {
      border-color: color-mix(in oklab, var(--color-neutral-500) 50%, transparent);
    }
  }

  .border-zinc-200 {
    border-color: var(--color-zinc-200);
  }

  .border-t-gray-600 {
    border-top-color: var(--color-gray-600);
  }

  .bg-black\/80 {
    background-color: #000c;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/80 {
      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }

  .bg-neutral-800 {
    background-color: var(--color-neutral-800);
  }

  .bg-pink-500\/30 {
    background-color: #f6339a4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-pink-500\/30 {
      background-color: color-mix(in oklab, var(--color-pink-500) 30%, transparent);
    }
  }

  .bg-pink-600 {
    background-color: var(--color-pink-600);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/90 {
    background-color: #ffffffe6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/90 {
      background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-pink-500\/20 {
    --tw-gradient-from: #f6339a33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-pink-500\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-pink-500) 20%, transparent);
    }
  }

  .from-pink-500\/20 {
    --tw-gradient-stops: var(
      --tw-gradient-via-stops,
      var(--tw-gradient-position),
      var(--tw-gradient-from) var(--tw-gradient-from-position),
      var(--tw-gradient-to) var(--tw-gradient-to-position)
    );
  }

  .to-purple-500\/20 {
    --tw-gradient-to: #ac4bff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-500\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
  }

  .to-purple-500\/20 {
    --tw-gradient-stops: var(
      --tw-gradient-via-stops,
      var(--tw-gradient-position),
      var(--tw-gradient-from) var(--tw-gradient-from-position),
      var(--tw-gradient-to) var(--tw-gradient-to-position)
    );
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pr-\[\.25em\] {
    padding-right: 0.25em;
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-start {
    text-align: start;
  }

  .align-middle {
    vertical-align: middle;
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .font-sans {
    font-family: var(--font-sans);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .font-black {
    --tw-font-weight: var(--font-weight-black);
    font-weight: var(--font-weight-black);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .text-nowrap {
    text-wrap: nowrap;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-blue-100 {
    color: var(--color-blue-100);
  }

  .text-blue-500 {
    color: var(--color-blue-500);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-700 {
    color: var(--color-blue-700);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-800\/50 {
    color: #1e293980;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-gray-800\/50 {
      color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);
    }
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-gray-950 {
    color: var(--color-gray-950);
  }

  .text-neutral-400 {
    color: var(--color-neutral-400);
  }

  .text-neutral-600 {
    color: var(--color-neutral-600);
  }

  .text-neutral-800 {
    color: var(--color-neutral-800);
  }

  .text-pink-600 {
    color: var(--color-pink-600);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-zinc-300 {
    color: var(--color-zinc-300);
  }

  .text-zinc-500 {
    color: var(--color-zinc-500);
  }

  .text-zinc-600 {
    color: var(--color-zinc-600);
  }

  .text-zinc-700 {
    color: var(--color-zinc-700);
  }

  .text-zinc-900 {
    color: var(--color-zinc-900);
  }

  .underline {
    text-decoration-line: underline;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-90 {
    opacity: 0.9;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a),
      0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow),
      var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow),
      var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a),
      0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow),
      var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
      var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia)
      var(--tw-drop-shadow);
  }

  .filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale)
      var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia)
      var(--tw-drop-shadow);
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
      var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
      var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
      var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
      var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
      var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
      var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  }

  .backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
      var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
      var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
      var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
      var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
      var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
      var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color,
      text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via,
      --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter,
      -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility,
      overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color,
      text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via,
      --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-150 {
    --tw-duration: 0.15s;
    transition-duration: 0.15s;
  }

  .duration-200 {
    --tw-duration: 0.2s;
    transition-duration: 0.2s;
  }

  .duration-300 {
    --tw-duration: 0.3s;
    transition-duration: 0.3s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .contain-content {
    contain: content;
  }

  .prose-zinc {
    --tw-prose-body: oklch(37% 0.013 285.805);
    --tw-prose-headings: oklch(21% 0.006 285.885);
    --tw-prose-lead: oklch(44.2% 0.017 285.786);
    --tw-prose-links: oklch(21% 0.006 285.885);
    --tw-prose-bold: oklch(21% 0.006 285.885);
    --tw-prose-counters: oklch(55.2% 0.016 285.938);
    --tw-prose-bullets: oklch(87.1% 0.006 286.286);
    --tw-prose-hr: oklch(92% 0.004 286.32);
    --tw-prose-quotes: oklch(21% 0.006 285.885);
    --tw-prose-quote-borders: oklch(92% 0.004 286.32);
    --tw-prose-captions: oklch(55.2% 0.016 285.938);
    --tw-prose-kbd: oklch(21% 0.006 285.885);
    --tw-prose-kbd-shadows: NaN NaN NaN;
    --tw-prose-code: oklch(21% 0.006 285.885);
    --tw-prose-pre-code: oklch(92% 0.004 286.32);
    --tw-prose-pre-bg: oklch(27.4% 0.006 286.033);
    --tw-prose-th-borders: oklch(87.1% 0.006 286.286);
    --tw-prose-td-borders: oklch(92% 0.004 286.32);
    --tw-prose-invert-body: oklch(87.1% 0.006 286.286);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.5% 0.015 286.067);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.5% 0.015 286.067);
    --tw-prose-invert-bullets: oklch(44.2% 0.017 285.786);
    --tw-prose-invert-hr: oklch(37% 0.013 285.805);
    --tw-prose-invert-quotes: oklch(96.7% 0.001 286.375);
    --tw-prose-invert-quote-borders: oklch(37% 0.013 285.805);
    --tw-prose-invert-captions: oklch(70.5% 0.015 286.067);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(87.1% 0.006 286.286);
    --tw-prose-invert-pre-bg: #00000080;
    --tw-prose-invert-th-borders: oklch(44.2% 0.017 285.786);
    --tw-prose-invert-td-borders: oklch(37% 0.013 285.805);
  }

  @media (hover: hover) {
    .group-hover\:underline:is(:where(.group):hover *) {
      text-decoration-line: underline;
    }

    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }

    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }

    .hover\:bg-zinc-50:hover {
      background-color: var(--color-zinc-50);
    }

    .hover\:bg-zinc-100:hover {
      background-color: var(--color-zinc-100);
    }

    .hover\:text-red-400:hover {
      color: var(--color-red-400);
    }

    .hover\:text-zinc-900:hover {
      color: var(--color-zinc-900);
    }

    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }

    .sm\:items-center {
      align-items: center;
    }

    .sm\:justify-between {
      justify-content: space-between;
    }
  }

  @media (min-width: 64rem) {
    .lg\:block {
      display: block;
    }

    .lg\:hidden {
      display: none;
    }

    .lg\:max-h-none {
      max-height: none;
    }

    .lg\:min-w-sm {
      min-width: var(--container-sm);
    }
  }

  @media (min-width: 80rem) {
    .xl\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  .dark\: block:is(.dark *) {
    display: block;
  }

  .dark\: hidden:is(.dark *) {
    display: none;
  }

  .dark\: border-gray-600:is(.dark *) {
    border-color: var(--color-gray-600);
  }

  .dark\: border-gray-700:is(.dark *) {
    border-color: var(--color-gray-700);
  }

  .dark\: border-neutral-600\/30:is(.dark *) {
    border-color: #5252524d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-600\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-600) 30%, transparent);
    }
  }

  .dark\: border-neutral-800:is(.dark *) {
    border-color: var(--color-neutral-800);
  }

  .dark\: border-sky-200:is(.dark *) {
    border-color: var(--color-sky-200);
  }

  .dark\: border-zinc-700:is(.dark *) {
    border-color: var(--color-zinc-700);
  }

  .dark\: border-t-gray-300:is(.dark *) {
    border-top-color: var(--color-gray-300);
  }

  .dark\: bg-blue-300:is(.dark *) {
    background-color: var(--color-blue-300);
  }

  .dark\: bg-blue-400:is(.dark *) {
    background-color: var(--color-blue-400);
  }

  .dark\: bg-blue-900\/20:is(.dark *) {
    background-color: #1c398e33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
    }
  }

  .dark\: bg-gray-600:is(.dark *) {
    background-color: var(--color-gray-600);
  }

  .dark\: bg-gray-800:is(.dark *) {
    background-color: var(--color-gray-800);
  }

  .dark\: bg-neutral-900:is(.dark *) {
    background-color: var(--color-neutral-900);
  }

  .dark\: bg-red-900\/20:is(.dark *) {
    background-color: #82181a33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
    }
  }

  .dark\: bg-zinc-900:is(.dark *) {
    background-color: var(--color-zinc-900);
  }

  .dark\: bg-zinc-900\/90:is(.dark *) {
    background-color: #18181be6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-zinc-900\/90:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-zinc-900) 90%, transparent);
    }
  }

  .dark\: text-blue-300:is(.dark *) {
    color: var(--color-blue-300);
  }

  .dark\: text-blue-400:is(.dark *) {
    color: var(--color-blue-400);
  }

  .dark\: text-gray-200:is(.dark *) {
    color: var(--color-gray-200);
  }

  .dark\: text-gray-200\/50:is(.dark *) {
    color: #e5e7eb80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-gray-200\/50:is(.dark *) {
      color: color-mix(in oklab, var(--color-gray-200) 50%, transparent);
    }
  }

  .dark\: text-gray-300:is(.dark *) {
    color: var(--color-gray-300);
  }

  .dark\: text-gray-400:is(.dark *) {
    color: var(--color-gray-400);
  }

  .dark\: text-gray-500:is(.dark *) {
    color: var(--color-gray-500);
  }

  .dark\: text-neutral-300:is(.dark *) {
    color: var(--color-neutral-300);
  }

  .dark\: text-neutral-400:is(.dark *) {
    color: var(--color-neutral-400);
  }

  .dark\: text-red-400:is(.dark *) {
    color: var(--color-red-400);
  }

  .dark\: text-red-500:is(.dark *) {
    color: var(--color-red-500);
  }

  .dark\: text-sky-200:is(.dark *) {
    color: var(--color-sky-200);
  }

  .dark\: text-white:is(.dark *) {
    color: var(--color-white);
  }

  .dark\: text-zinc-100:is(.dark *) {
    color: var(--color-zinc-100);
  }

  .dark\: text-zinc-200:is(.dark *) {
    color: var(--color-zinc-200);
  }

  .dark\: text-zinc-300:is(.dark *) {
    color: var(--color-zinc-300);
  }

  .dark\: text-zinc-400:is(.dark *) {
    color: var(--color-zinc-400);
  }

  .dark\: prose-invert:is(.dark *) {
    --tw-prose-body: var(--tw-prose-invert-body);
    --tw-prose-headings: var(--tw-prose-invert-headings);
    --tw-prose-lead: var(--tw-prose-invert-lead);
    --tw-prose-links: var(--tw-prose-invert-links);
    --tw-prose-bold: var(--tw-prose-invert-bold);
    --tw-prose-counters: var(--tw-prose-invert-counters);
    --tw-prose-bullets: var(--tw-prose-invert-bullets);
    --tw-prose-hr: var(--tw-prose-invert-hr);
    --tw-prose-quotes: var(--tw-prose-invert-quotes);
    --tw-prose-quote-borders: var(--tw-prose-invert-quote-borders);
    --tw-prose-captions: var(--tw-prose-invert-captions);
    --tw-prose-kbd: var(--tw-prose-invert-kbd);
    --tw-prose-kbd-shadows: var(--tw-prose-invert-kbd-shadows);
    --tw-prose-code: var(--tw-prose-invert-code);
    --tw-prose-pre-code: var(--tw-prose-invert-pre-code);
    --tw-prose-pre-bg: var(--tw-prose-invert-pre-bg);
    --tw-prose-th-borders: var(--tw-prose-invert-th-borders);
    --tw-prose-td-borders: var(--tw-prose-invert-td-borders);
  }

  @media (hover: hover) {
    .dark\:hover\:bg-zinc-800:is(.dark *):hover {
      background-color: var(--color-zinc-800);
    }

    .dark\:hover\:text-zinc-100:is(.dark *):hover {
      color: var(--color-zinc-100);
    }
  }

  .\[\&_svg\]\: text-zinc-500 svg {
    color: var(--color-zinc-500);
  }

  @media (hover: hover) {
    .hover\:\[\&_svg\]\:text-zinc-900:hover svg {
      color: var(--color-zinc-900);
    }

    .dark\:hover\:\[\&_svg\]\:text-zinc-100:is(.dark *):hover svg {
      color: var(--color-zinc-100);
    }
  }
}

@keyframes scrollText {
  0% {
    transform: translate(0);
  }

  to {
    transform: translate(calc(-1 * var(--text-width)));
  }
}

.animate-scroll-text {
  animation: scrollText;
}

.ambilight {
  filter: url(#ambilight);
}

.centered-layout {
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  min-height: 100vh;
  display: flex;
}

.default-layout {
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  min-height: 100vh;
  display: flex;
}

@keyframes slide-up {
  0% {
    opacity: 0;
    filter: blur(4px);
  }

  to {
    opacity: 1;
    filter: blur();
  }
}

@keyframes slide-down {
  0% {
    opacity: 1;
    filter: blur();
  }

  to {
    opacity: 0;
    filter: blur(4px);
  }
}

::view-transition-old(page-container) {
  animation: 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards slide-down;
}

::view-transition-new(page-container) {
  animation: 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards slide-up;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false;
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-blur {
  syntax: "*";
  inherits: false;
}

@property --tw-brightness {
  syntax: "*";
  inherits: false;
}

@property --tw-contrast {
  syntax: "*";
  inherits: false;
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}

@property --tw-invert {
  syntax: "*";
  inherits: false;
}

@property --tw-opacity {
  syntax: "*";
  inherits: false;
}

@property --tw-saturate {
  syntax: "*";
  inherits: false;
}

@property --tw-sepia {
  syntax: "*";
  inherits: false;
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}

@property --tw-duration {
  syntax: "*";
  inherits: false;
}

@property --tw-ease {
  syntax: "*";
  inherits: false;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
