'use client';

import React, { useState, ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'motion/react';
import Navigation from './Navigation';
import { ColorProvider } from '../contexts/ColorContext';

interface PageLayoutProps {
  children: ReactNode;
}

export default function PageLayout({
  children,
}: PageLayoutProps): React.JSX.Element {
  const pathname = usePathname();
  const isHome = pathname === '/';
  const [selectedColor, setSelectedColor] = useState<string>('blue');

  return (
    <div
      className="min-h-screen flex justify-center"
      style={{
        backgroundColor: isHome ? 'var(--black-1)' : 'var(--black-2)',
      }}
    >
      <motion.div
        className="mx-auto"
        style={{ padding: '16px' }}
        animate={{
          maxWidth: isHome ? '520px' : '674px',
        }}
        transition={{
          duration: 0.4,
          ease: [0.25, 0.46, 0.45, 0.94], // Custom cubic-bezier
        }}
      >
        <ColorProvider
          selectedColor={selectedColor}
          setSelectedColor={setSelectedColor}
        >
          <Navigation
            selectedColor={selectedColor}
            setSelectedColor={setSelectedColor}
          />
          <AnimatePresence mode="wait">
            <motion.div
              key={pathname}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.3,
                ease: 'easeOut',
              }}
            >
              {children}
            </motion.div>
          </AnimatePresence>
        </ColorProvider>
      </motion.div>
    </div>
  );
}
