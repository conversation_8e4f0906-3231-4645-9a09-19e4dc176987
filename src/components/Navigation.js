'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { motion } from 'motion/react';

const colorOptions = [
  {
    name: 'yellow',
    value: 'var(--hue-0-transparent)',
    solidColor: 'var(--yellow)',
    textColor: 'var(--yellow)',
  },
  {
    name: 'blue',
    value: 'var(--hue-5-transparent)',
    solidColor: 'var(--hue-5)',
    textColor: 'var(--hue-5)',
  },
  {
    name: 'pink',
    value: 'var(--hue-1-transparent)',
    solidColor: 'var(--hue-1)',
    textColor: 'var(--hue-1)',
  },
];

export default function Navigation() {
  const pathname = usePathname();
  const [selectedColor, setSelectedColor] = useState('yellow');

  const getLogoBackgroundColor = () => {
    const color = colorOptions.find((c) => c.name === selectedColor);
    return color ? color.value : 'var(--hue-0-transparent)';
  };

  const getLogoTextColor = () => {
    const color = colorOptions.find((c) => c.name === selectedColor);
    return color ? color.textColor : 'var(--yellow)';
  };

  const navigationItems = [
    { href: '/', label: 'home' },
    { href: '/about', label: 'about' },
    { href: '/cv', label: 'work' },
    { href: '/posts', label: 'posts' },
  ];

  return (
    <nav className="h-[30px] flex items-center justify-between">
      {/* Logo and Navigation Links */}
      <div className="flex items-center gap-4">
        {/* Logo */}
        <div
          className="px-2 py-1 rounded-lg text-[20px] hover:scale-105 transition-all duration-200 cursor-pointer flex items-center justify-center"
          style={{
            backgroundColor: getLogoBackgroundColor(),
            color: getLogoTextColor(),
            fontFamily: 'var(--font-rammetto-one)',
          }}
        >
          V
        </div>

        {/* Navigation Links */}
        <div className="flex items-center gap-4">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`text-base transition-colors duration-200 ${
                  isActive
                    ? 'text-[var(--white-feint-1)] underline'
                    : 'text-[var(--white-feint-1)] hover:text-white'
                }`}
              >
                {item.label}
              </Link>
            );
          })}
        </div>
      </div>

      {/* Color Selector */}
      <div className="relative flex items-center gap-2">
        {/* Animated White Ring with Motion */}
        <motion.div
          className="absolute w-6 h-6 rounded-full border-2 border-white pointer-events-none"
          animate={{
            x: colorOptions.findIndex((c) => c.name === selectedColor) * 24 - 4,
          }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30,
          }}
        />

        {/* Color Options */}
        {colorOptions.map((color) => (
          <motion.button
            key={color.name}
            className="w-4 h-4 rounded-full border-2 border-transparent focus:outline-none cursor-pointer relative z-10"
            style={{ backgroundColor: color.solidColor }}
            onClick={() => setSelectedColor(color.name)}
            aria-label={`Select ${color.name} color`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: 'spring', stiffness: 400, damping: 25 }}
          />
        ))}
      </div>
    </nav>
  );
}
