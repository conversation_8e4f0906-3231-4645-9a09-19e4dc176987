'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

const colorOptions = [
  { name: 'yellow', value: 'var(--yellow)' },
  { name: 'blue', value: 'var(--blue)' },
  { name: 'hue-1', value: 'var(--hue-1)' },
];

export default function Navigation() {
  const pathname = usePathname();
  const [selectedColor, setSelectedColor] = useState('yellow');

  const getLogoBackgroundColor = () => {
    const color = colorOptions.find((c) => c.name === selectedColor);
    return color ? color.value : 'var(--yellow)';
  };

  const navigationItems = [
    { href: '/', label: 'home' },
    { href: '/about', label: 'about' },
    { href: '/cv', label: 'work' },
    { href: '/posts', label: 'posts' },
  ];

  return (
    <nav className="h-[30px] flex items-center justify-between">
      {/* Logo and Navigation Links */}
      <div className="flex items-center gap-4">
        {/* Logo */}
        <div
          className="px-2 py-1 rounded-lg text-[18px] hover:scale-105 transition-transform cursor-pointer"
          style={{
            backgroundColor: getLogoBackgroundColor(),
            fontFamily: 'var(--font-rammetto-one)',
          }}
        >
          pvr
        </div>

        {/* Navigation Links */}
        <div className="flex items-center gap-4">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`text-base transition-colors duration-200 ${
                  isActive
                    ? 'text-[var(--white-feint-1)] underline'
                    : 'text-[var(--white-feint-1)] hover:text-white'
                }`}
              >
                {item.label}
              </Link>
            );
          })}
        </div>
      </div>

      {/* Color Selector */}
      <div className="flex items-center gap-2">
        {colorOptions.map((color) => (
          <button
            key={color.name}
            className="w-4 h-4 rounded-full border-2 border-transparent focus:outline-none focus:ring-2 focus:ring-white/20 hover:scale-110 transition-transform cursor-pointer"
            style={{ backgroundColor: color.value }}
            onClick={() => setSelectedColor(color.name)}
          />
        ))}
      </div>
    </nav>
  );
}
