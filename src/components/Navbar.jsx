"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

export default function Navbar() {
  const pathname = usePathname()

  const links = [
    { href: "/", label: "home" },
    { href: "/about", label: "about" },
    { href: "/cv", label: "work" },
    { href: "/posts", label: "posts" },
  ]

  const isHome = pathname === "/"

  return (
    <nav
      className={
        isHome
          ? "flex justify-center space-x-6 text-feint-text mt-8"
          : "fixed top-0 left-0 right-0 bg-bg-primary border-b border-border z-50"
      }
    >
      <div
        className={
          isHome
            ? "flex space-x-6"
            : "max-w-4xl mx-auto flex justify-center space-x-6 py-4"
        }
      >
        {links.map((link) => (
          <Link
            key={link.href}
            href={link.href}
            className={`hover:text-accent ${
              pathname === link.href ? "text-accent font-semibold" : "text-feint-text"
            }`}
          >
            {link.label}
          </Link>
        ))}
      </div>
    </nav>
  )
}
