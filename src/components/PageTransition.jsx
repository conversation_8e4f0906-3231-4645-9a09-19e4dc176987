"use client"

import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

export default function PageTransition({ children }) {
  const pathname = usePathname()
  const [displayedPath, setDisplayedPath] = useState(pathname)
  const [isExiting, setIsExiting] = useState(false)

  useEffect(() => {
    if (pathname === displayedPath) return

    // Start exit animation
    setIsExiting(true)

    // After exit animation completes, update path and start enter animation
    const timer = setTimeout(() => {
      setDisplayedPath(pathname)
      setIsExiting(false)
    }, 250) // Match CSS animation duration

    return () => clearTimeout(timer)
  }, [pathname, displayedPath])

  return (
    <div
      key={displayedPath}
      className={isExiting ? "page-transition-exit" : "page-transition-enter"}
    >
      {children}
    </div>
  )
}
