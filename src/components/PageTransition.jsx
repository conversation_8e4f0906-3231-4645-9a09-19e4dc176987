"use client"

import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

export default function PageTransition({ children }) {
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    setIsLoading(true)

    // Reset loading state after a brief moment to trigger enter animation
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 50)

    return () => clearTimeout(timer)
  }, [pathname])

  return (
    <div
      key={pathname}
      className={`transition-all duration-300 ease-out ${
        isLoading
          ? "opacity-0 translate-y-2 blur-sm"
          : "opacity-100 translate-y-0 blur-none"
      }`}
    >
      {children}
    </div>
  )
}
