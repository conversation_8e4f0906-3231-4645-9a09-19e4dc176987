"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState, useEffect } from "react"

export default function Header() {
  const pathname = usePathname()
  const [theme, setTheme] = useState("light")

  useEffect(() => {
    // Check for saved theme preference or default to 'light'
    const savedTheme = localStorage.getItem("theme")
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches

    if (savedTheme) {
      setTheme(savedTheme)
      document.documentElement.classList.toggle("dark", savedTheme === "dark")
    } else {
      const defaultTheme = prefersDark ? "dark" : "light"
      setTheme(defaultTheme)
      document.documentElement.classList.toggle("dark", defaultTheme === "dark")
    }
  }, [])

  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light"
    setTheme(newTheme)
    localStorage.setItem("theme", newTheme)
    document.documentElement.classList.toggle("dark", newTheme === "dark")
  }

  const links = [
    { href: "/", label: "home" },
    { href: "/cv", label: "work" },
    { href: "/posts", label: "posts" },
    { href: "/about", label: "about" },
  ]

  return (
    <header className="mb-6 flex items-center justify-between">
      <nav className="flex items-center gap-4 text-gray-950 dark:text-gray-300">
        {links.map((link) => (
          <Link
            key={link.href}
            href={link.href}
            className="font-medium text-zinc-500 hover:text-zinc-900 hover:underline dark:hover:text-zinc-100 transition-colors"
          >
            {link.label}
          </Link>
        ))}
      </nav>
      
      <button
        onClick={toggleTheme}
        className="inline-flex items-center rounded-lg border p-1.5 transition-colors hover:bg-zinc-50 dark:border-zinc-700 dark:hover:bg-zinc-800 [&_svg]:text-zinc-500 hover:[&_svg]:text-zinc-900 dark:hover:[&_svg]:text-zinc-100"
        aria-label="Toggle theme"
      >
        <span className="sr-only">Toggle theme</span>
        
        {/* Sun icon for dark mode */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 16 16"
          fill="currentColor"
          className={`size-4 ${theme === "dark" ? "block" : "hidden"}`}
        >
          <path d="M8 1a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 8 1ZM10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM12.95 4.11a.75.75 0 1 0-1.06-1.06l-1.062 1.06a.75.75 0 0 0 1.061 1.062l1.06-1.061ZM15 8a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5A.75.75 0 0 1 15 8ZM11.89 12.95a.75.75 0 0 0 1.06-1.06l-1.06-1.062a.75.75 0 0 0-1.062 1.061l1.061 1.06ZM8 12a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 8 12ZM5.172 11.89a.75.75 0 0 0-1.061-1.062L3.05 11.89a.75.75 0 1 0 1.06 1.06l1.06-1.06ZM4 8a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5A.75.75 0 0 1 4 8ZM4.11 5.172A.75.75 0 0 0 5.173 4.11L4.11 3.05a.75.75 0 1 0-1.06 1.06l1.06 1.06Z" />
        </svg>
        
        {/* Moon icon for light mode */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 16 16"
          fill="currentColor"
          className={`size-4 ${theme === "light" ? "block" : "hidden"}`}
        >
          <path d="M14.438 10.148c.19-.425-.321-.787-.748-.601A5.5 5.5 0 0 1 6.453 2.31c.186-.427-.176-.938-.6-.748a6.501 6.501 0 1 0 8.585 8.586Z" />
        </svg>
      </button>
    </header>
  )
}
