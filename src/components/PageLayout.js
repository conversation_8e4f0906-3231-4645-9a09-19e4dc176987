'use client';

import { usePathname } from 'next/navigation';
import Navigation from './Navigation';

export default function PageLayout({ children }) {
  const pathname = usePathname();
  const isHome = pathname === '/';

  const containerStyles = {
    maxWidth: '674px',
    padding: '16px',
    backgroundColor: isHome ? 'var(--black-1)' : 'var(--black-2)',
  };

  return (
    <div
      className={
        isHome
          ? 'h-screen flex justify-center'
          : 'min-h-screen flex justify-center'
      }
      style={{ backgroundColor: containerStyles.backgroundColor }}
    >
      <div className="w-full mx-auto" style={containerStyles}>
        <Navigation />
        <div className="mt-6">{children}</div>
      </div>
    </div>
  );
}
