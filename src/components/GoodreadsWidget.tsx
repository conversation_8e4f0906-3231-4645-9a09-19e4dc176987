'use client';

import React, { useState, useEffect } from 'react';
import { ExternalLink, RotateCcw } from 'lucide-react';
import { fetchCurrentlyReadingBooks } from '../utils/goodreadsApi';

interface Book {
  id: string;
  title: string;
  author: string;
  coverUrl: string;
  goodreadsUrl: string;
}

export default function GoodreadsWidget(): React.JSX.Element {
  const [books, setBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBooks();
  }, []);

  const fetchBooks = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const result = await fetchCurrentlyReadingBooks();

      if (result.success) {
        setBooks(result.data || []);
      } else {
        setError(result.error || 'Unknown error occurred');
        setBooks([]);
      }
    } catch (err) {
      console.error('Error fetching books:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setBooks([]);
    } finally {
      setLoading(false);
    }
  };

  const refreshBooks = (): void => {
    fetchBooks();
  };

  if (loading) {
    return (
      <div className="mt-8">
        <h2 className="text-white text-base font-atkinson mb-5">
          What I&apos;m currently reading:
        </h2>
        <div className="flex items-center gap-3">
          <div className="w-[39px] h-[60px] bg-white/20 rounded animate-pulse"></div>
          <div className="flex-1">
            <div className="h-4 bg-white/20 rounded animate-pulse mb-2"></div>
            <div className="h-3 bg-white/20 rounded w-3/4 animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-8">
        <div className="flex items-center justify-between mb-5">
          <h2 className="text-white text-base font-atkinson">
            What I&apos;m currently reading:
          </h2>
        </div>
        <p className="text-white/60 text-sm mb-2">
          Unable to load books at the moment.
        </p>
        <button
          onClick={refreshBooks}
          className="p-1 border border-white/20 rounded hover:border-white/40 hover:bg-white/10 transition-all duration-200 cursor-pointer group"
        >
          <RotateCcw
            size={12}
            className="text-white/60 group-hover:text-white transition-colors duration-200"
          />
        </button>
      </div>
    );
  }

  return (
    <div className="mt-8">
      <div className="flex items-center justify-between mb-5">
        <h2 className="text-white text-base font-atkinson">
          <a
            href="https://www.goodreads.com/review/list/162391550-veerendranath?shelf=currently-reading"
            target="_blank"
            rel="noopener noreferrer"
            className="text-white no-underline hover:underline transition-colors duration-200"
          >
            What I&apos;m currently reading:
          </a>
        </h2>
        <button
          onClick={refreshBooks}
          className="text-white/60 text-xs hover:text-white transition-colors duration-200 cursor-pointer"
        >
          Refresh
        </button>
      </div>

      <div className="space-y-4">
        {books.map((book: Book) => (
          <a
            key={book.id}
            href={book.goodreadsUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-3 relative group p-2 cursor-pointer rounded transition-all duration-300 ease-out"
          >
            <img
              src={book.coverUrl}
              alt={book.title}
              className="w-[39px] h-[60px] object-cover flex-shrink-0"
            />
            <div className="min-w-0 flex-1">
              <h3 className="text-white text-sm font-medium mb-1">
                {book.title}
              </h3>
              <p className="text-white/60 text-xs">by {book.author}</p>
            </div>
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out">
              <ExternalLink size={14} color="var(--white)" />
            </div>
          </a>
        ))}
      </div>
    </div>
  );
}
