'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion } from 'motion/react';

import { colorOptions, getSelectedColorValue } from '../contexts/ColorContext';

interface NavigationProps {
  selectedColor: string;
  setSelectedColor: (color: string) => void;
}

interface NavigationItem {
  href: string;
  label: string;
}

export default function Navigation({
  selectedColor,
  setSelectedColor,
}: NavigationProps): React.JSX.Element {
  const pathname = usePathname();

  const getLogoTextColor = (): string => {
    return getSelectedColorValue(selectedColor);
  };

  const navigationItems: NavigationItem[] = [
    { href: '/', label: 'home' },
    { href: '/about', label: 'about' },
    { href: '/cv', label: 'work' },
    { href: '/posts', label: 'posts' },
  ];

  return (
    <nav className="h-[30px] flex items-center justify-between mb-6">
      {/* Logo and Navigation Links */}
      <div className="flex items-center gap-4">
        {/* Logo */}
        <h3
          className="text-[18px] cursor-pointer transition-colors duration-300 ease-out"
          style={{
            color: getLogoTextColor(),
            fontFamily: 'var(--font-rammetto-one)',
          }}
        >
          pvr
        </h3>

        {/* Navigation Links */}
        <div className="flex items-center gap-4 font-medium">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <motion.div
                key={item.href}
                transition={{
                  type: 'spring',
                  stiffness: 400,
                  damping: 25,
                }}
              >
                <Link
                  href={item.href}
                  className={`text-base ${
                    isActive
                      ? 'text-[var(--white)]'
                      : 'text-[var(--white-feint-1)] hover:text-white'
                  }`}
                  style={{
                    transition: 'color 0.2s ease-out',
                  }}
                >
                  {item.label}
                </Link>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Color Selector */}
      <div className="relative flex items-center gap-2">
        {/* Animated White Ring with Motion */}
        <motion.div
          className="absolute w-6 h-6 rounded-full border-2 border-white pointer-events-none"
          initial={{
            x: colorOptions.findIndex((c) => c.name === selectedColor) * 24 - 4,
          }}
          animate={{
            x: colorOptions.findIndex((c) => c.name === selectedColor) * 24 - 4,
          }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30,
          }}
        />

        {/* Color Options */}
        {colorOptions.map((color) => (
          <motion.button
            key={color.name}
            className="w-4 h-4 rounded-full border-2 border-transparent focus:outline-none cursor-pointer relative z-10"
            style={{ backgroundColor: color.color }}
            onClick={() => setSelectedColor(color.name)}
            aria-label={`Select ${color.name} color`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: 'spring', stiffness: 400, damping: 25 }}
          />
        ))}
      </div>
    </nav>
  );
}
