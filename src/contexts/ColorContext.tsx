'use client';

import { createContext, useContext, ReactNode } from 'react';

// Types
export interface ColorOption {
  name: string;
  color: string;
}

export interface ColorContextType {
  selectedColor: string;
  setSelectedColor: (color: string) => void;
}

// Shared color options
export const colorOptions: ColorOption[] = [
  {
    name: 'yellow',
    color: 'var(--yellow)',
  },
  {
    name: 'blue',
    color: 'var(--blue)',
  },
  {
    name: 'green',
    color: 'var(--hue-1)',
  },
];

// Create a context for the selected color
const ColorContext = createContext<ColorContextType | undefined>(undefined);

interface ColorProviderProps {
  children: ReactNode;
  selectedColor: string;
  setSelectedColor: (color: string) => void;
}

export const ColorProvider: React.FC<ColorProviderProps> = ({
  children,
  selectedColor,
  setSelectedColor,
}) => {
  return (
    <ColorContext.Provider value={{ selectedColor, setSelectedColor }}>
      {children}
    </ColorContext.Provider>
  );
};

export const useColor = (): string => {
  const context = useContext(ColorContext);
  if (!context) {
    throw new Error('useColor must be used within a ColorProvider');
  }
  return context.selectedColor;
};

export const useColorContext = (): ColorContextType => {
  const context = useContext(ColorContext);
  if (!context) {
    throw new Error('useColorContext must be used within a ColorProvider');
  }
  return context;
};

// Shared utility function to get selected color value
export const getSelectedColorValue = (selectedColor: string): string => {
  const color = colorOptions.find((c) => c.name === selectedColor);
  return color ? color.color : 'var(--blue)';
};
