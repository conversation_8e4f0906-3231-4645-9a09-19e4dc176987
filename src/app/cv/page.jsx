export default function Work() {
  return (
    <div className="mb-[20vh] flex w-full flex-col items-start space-y-4">
      <p className="mb-4 text-3xl font-semibold">Experience</p>
      <div className="mb-6 space-y-2">
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-xl font-medium">
            Full-Stack Developer
            <span className="text-xl font-normal">
              {" "}at{" "}
              <a
                className="text-xl font-medium text-blue-700 dark:text-blue-300"
                href="https://example.com"
              >
                Your Company
              </a>
            </span>
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            January 2023 - present
          </p>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Building modern web applications with React, Next.js, and Node.js. 
          Focus on creating scalable solutions and improving user experience.
        </p>
        <ul className="ml-3 list-outside list-disc space-y-2 text-sm leading-relaxed">
          <li>
            Developed and maintained multiple full-stack web applications using React, Next.js, and TypeScript
          </li>
          <li>
            Implemented responsive designs and optimized performance, resulting in improved user engagement
          </li>
          <li>
            Collaborated with cross-functional teams to deliver high-quality software solutions on time
          </li>
        </ul>
      </div>

      <p className="mb-4 text-3xl font-semibold">Personal Projects & Open Source</p>
      <div className="mb-6 space-y-6">
        {/* Project 1 */}
        <div className="space-y-2">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h3 className="text-lg font-medium">Portfolio Website</h3>
            <div className="text-sm text-blue-600 dark:text-blue-400">
              <a
                href="https://github.com/veerendranath0312/portfolio"
                target="_blank"
                className="mr-3 hover:underline"
              >
                GitHub
              </a>
              <a href="https://veerendranath.tech" target="_blank" className="hover:underline">
                Live Demo
              </a>
            </div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Next.js, React, TypeScript, TailwindCSS
          </p>
          <p className="text-sm">
            Personal portfolio website built with Next.js and TailwindCSS, featuring responsive design 
            and modern web development practices.
          </p>
        </div>

        {/* Project 2 */}
        <div className="space-y-2">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h3 className="text-lg font-medium">Task Management App</h3>
            <div className="text-sm text-blue-600 dark:text-blue-400">
              <a
                href="https://github.com/veerendranath0312/task-manager"
                target="_blank"
                className="hover:underline"
              >
                GitHub
              </a>
            </div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            React, Node.js, Express, MongoDB
          </p>
          <p className="text-sm">
            Full-stack task management application with user authentication, 
            real-time updates, and intuitive drag-and-drop interface.
          </p>
        </div>

        {/* Project 3 */}
        <div className="space-y-2">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h3 className="text-lg font-medium">Weather Dashboard</h3>
            <div className="text-sm text-blue-600 dark:text-blue-400">
              <a
                href="https://github.com/veerendranath0312/weather-dashboard"
                target="_blank"
                className="mr-3 hover:underline"
              >
                GitHub
              </a>
              <a href="https://weather-dashboard-demo.vercel.app" target="_blank" className="hover:underline">
                Live Demo
              </a>
            </div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            React, TypeScript, OpenWeather API, Chart.js
          </p>
          <p className="text-sm">
            Interactive weather dashboard with location-based forecasts, 
            data visualization, and responsive design for all devices.
          </p>
        </div>
      </div>
    </div>
  )
}
