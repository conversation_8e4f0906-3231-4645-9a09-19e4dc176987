export default function Posts() {
  // Sam<PERSON> posts data - replace with your actual posts
  const posts = [
    {
      title: "Building Modern Web Applications with Next.js",
      description: "A comprehensive guide to building scalable web apps.",
      date: "December 15, 2024",
      slug: "nextjs-modern-web-apps",
    },
    {
      title: "Understanding React Server Components",
      description: "Deep dive into the new paradigm of server-side rendering.",
      date: "November 28, 2024",
      slug: "react-server-components",
    },
    {
      title: "TypeScript Best Practices for Large Projects",
      description: "Tips and patterns for maintaining type safety at scale.",
      date: "October 10, 2024",
      slug: "typescript-best-practices",
    },
  ]

  return (
    <div>
      <ul className="flex flex-col gap-1.5">
        {posts.map((post) => (
          <li key={post.slug}>
            <a
              href={`/posts/${post.slug}`}
              className="group flex justify-between gap-3 pb-3"
            >
              <div>
                <div className="group-hover:underline">{post.title}</div>
                <div className="text-neutral-400">{post.description}</div>
              </div>
              <span className="text-nowrap text-zinc-500">{post.date}</span>
            </a>
          </li>
        ))}
      </ul>
    </div>
  )
}
