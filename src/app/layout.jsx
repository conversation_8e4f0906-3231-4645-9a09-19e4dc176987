import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_Mono,
  Atkinson_Hyperlegible_Next,
  Baskervville,
  Rammetto_One,
} from "next/font/google"
import Header from "@/components/Header"
import PageTransition from "../components/PageTransition"
import "./globals.css"

const atkinson = Atkinson_Hyperlegible_Next({
  variable: "--font-atkinson",
  subsets: ["latin"],
  weight: ["200", "400", "700", "800"],
})

const baskervville = Baskervville({
  variable: "--font-baskervville",
  subsets: ["latin"],
  weight: ["400", "700"],
  style: ["normal", "italic"],
})

const rammetto = Rammetto_One({
  variable: "--font-rammetto",
  subsets: ["latin"],
  weight: ["400"],
})

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
})

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
})

export const metadata = {
  title: "<PERSON><PERSON><PERSON><PERSON>'s blog",
  description:
    "a blog about web development, design, and other things I find interesting",
}

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="light">
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                function setTheme() {
                  let theme;
                  if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
                    theme = localStorage.getItem('theme');
                  } else {
                    theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                  }
                  document.documentElement.classList.toggle('dark', theme === 'dark');
                }
                setTheme();
              })();
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${atkinson.variable} ${baskervville.variable} ${rammetto.variable} max-h-max min-h-screen w-screen max-w-screen overflow-x-clip font-sans text-zinc-900 antialiased transition-colors dark:bg-zinc-900 dark:text-zinc-200`}
      >
        <PageTransition>
          <div className="mx-auto min-h-screen flex flex-col justify-center p-4 max-w-lg">
            <div className="flex flex-col space-y-6">
              <Header />
              <main>{children}</main>
            </div>
          </div>
        </PageTransition>
      </body>
    </html>
  )
}
