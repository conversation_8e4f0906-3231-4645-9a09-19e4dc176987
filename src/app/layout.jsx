import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_Mono,
  Atkinson_Hyperlegible_Next,
  Baskervville,
  Rammetto_One,
} from "next/font/google"
import Navbar from "@/components/Navbar"
import PageTransition from "../components/PageTransition"
import "./globals.css"

const atkinson = Atkinson_Hyperlegible_Next({
  variable: "--font-atkinson",
  subsets: ["latin"],
  weight: ["200", "400", "700", "800"],
})

const baskervville = Baskervville({
  variable: "--font-baskervville",
  subsets: ["latin"],
  weight: ["400", "700"],
  style: ["normal", "italic"],
})

const rammetto = Rammetto_One({
  variable: "--font-rammetto",
  subsets: ["latin"],
  weight: ["400"],
})

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
})

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
})

export const metadata = {
  title: "ve<PERSON><PERSON><PERSON>'s blog",
  description: "A personal blog by veere<PERSON><PERSON>",
}

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${atkinson.variable} ${baskervville.variable} ${rammetto.variable} antialiased`}
      >
        <PageTransition>
          <Navbar />
          <div className="bg-bg-primary text-text font-atkinson">{children}</div>
        </PageTransition>
      </body>
    </html>
  )
}
