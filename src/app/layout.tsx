import React, { ReactNode } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_Mono,
  Atkinson_Hyperlegible,
  Rammetto_One,
  Azeret_Mono,
} from 'next/font/google';
import { Metadata } from 'next';
import './globals.css';
import PageLayout from '../components/PageLayout';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

const atkinsonHyperlegible = Atkinson_Hyperlegible({
  variable: '--font-atkinson-hyperlegible',
  subsets: ['latin'],
  weight: ['400', '700'],
});

const rammettoOne = Rammetto_One({
  variable: '--font-rammetto-one',
  subsets: ['latin'],
  weight: ['400'],
});

const azeretMono = Azeret_Mono({
  variable: '--font-azeret-mono',
  subsets: ['latin'],
  weight: ['400', '700'],
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON><PERSON>'s blog",
  description:
    'a blog about web development, design, and other things I find interesting',
  icons: {
    icon: '/ghost.svg',
    shortcut: '/ghost.svg',
    apple: '/ghost.svg',
  },
};

interface RootLayoutProps {
  children: ReactNode;
}

export default function RootLayout({
  children,
}: RootLayoutProps): React.JSX.Element {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${atkinsonHyperlegible.variable} ${rammettoOne.variable} ${azeretMono.variable} antialiased`}
      >
        <PageLayout>{children}</PageLayout>
      </body>
    </html>
  );
}
