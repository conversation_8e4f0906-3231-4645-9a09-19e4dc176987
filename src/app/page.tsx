'use client';

import React, { useState, useEffect } from 'react';
import { Github, Linkedin } from 'lucide-react';
import { motion } from 'motion/react';
import { useColor, getSelectedColorValue } from '../contexts/ColorContext';
import GoodreadsWidget from '../components/GoodreadsWidget';

export default function Home(): React.JSX.Element {
  const selectedColor: string = useColor();

  // Use useEffect to ensure client-side rendering matches server
  const [mounted, setMounted] = useState<boolean>(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="flex flex-col">
      {/* Profile Image */}
      <motion.img
        src="/profile.png"
        alt="Profile"
        className="w-[170px] h-[170px] mt-16 mb-2 rounded-full object-center object-cover border border-white/6 grayscale"
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{
          duration: 0.8,
          ease: [0.25, 0.46, 0.45, 0.94],
          delay: 0.2,
        }}
      />

      {/* Name Heading */}
      <h2 className="text-white font-bold text-[30px] font-atkinson">
        veerendranath p.
      </h2>

      {/* Sub Heading */}
      <p className="text-white text-[20px] font-atkinson tracking-tight leading-[145%]">
        software, full-stack, books - kent, oh.
      </p>

      {/* Bio Text */}
      <motion.div
        className="text-[var(--white-feint-1)] text-[16px] font-atkinson leading-relaxed"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.8,
          ease: [0.25, 0.46, 0.45, 0.94],
          delay: 0.6,
        }}
      >
        <p className="mt-5">
          I&apos;m a software developer who{' '}
          <img
            src={
              selectedColor === 'yellow'
                ? '/love-yellow.svg'
                : selectedColor === 'blue'
                ? '/love-blue.svg'
                : '/love-hue-1.svg'
            }
            alt="love"
            className="w-7 h-7 inline brightness-[0.8] transition-opacity duration-300 ease-out align-top -mt-1"
          />
          &apos;s turning{' '}
          <span
            className="transition-all duration-300 ease-out"
            style={{
              color: mounted
                ? getSelectedColorValue(selectedColor)
                : 'var(--blue)',
            }}
          >
            ideas into applications
          </span>{' '}
          that connect with people.
        </p>

        <p className="mt-5">
          With 4 years of experience, including my time as a backend engineer at
          Wipro building critical microservices for HP, I engineer full-stack
          solutions —{' '}
          <span
            className="transition-all duration-300 ease-out"
            style={{
              color: mounted
                ? getSelectedColorValue(selectedColor)
                : 'var(--blue)',
            }}
          >
            from intuitive frontends to robust and scalable backends
          </span>
          .
        </p>
        <p className="mt-5">
          I believe that the best products are both{' '}
          <span
            className="transition-all duration-300 ease-out"
            style={{
              color: mounted
                ? getSelectedColorValue(selectedColor)
                : 'var(--blue)',
            }}
          >
            powerful and simple
          </span>
          . My goal is always to build high-performance web solutions that are a
          delight to use.
        </p>

        <p className="mt-5">
          When I&apos;m not at work, you&apos;ll find me traveling or captivated
          by a good thriller. I am always happy to chat about web tech, films,
          books, or whatever project is currently consuming my brain.
        </p>
      </motion.div>

      {/* Social Icons */}
      <motion.div
        className="flex gap-3 mt-4"
        initial="hidden"
        animate="visible"
        variants={{
          hidden: { opacity: 0, y: 20 },
          visible: {
            opacity: 1,
            y: 0,
            transition: {
              duration: 0.6,
              ease: 'easeOut',
              delay: 1.4, // Start after bio text animation completes (0.6s delay + 0.8s duration)
              staggerChildren: 0.2,
            },
          },
        }}
      >
        <motion.a
          href="https://github.com/veerendranath0312"
          target="_blank"
          rel="noopener noreferrer"
          className="transition-all duration-300 ease-out hover:opacity-80"
          variants={{
            hidden: { opacity: 0, scale: 0.8, rotate: -10 },
            visible: {
              opacity: 1,
              scale: 1,
              rotate: 0,
              transition: {
                duration: 0.5,
                ease: 'easeOut',
                type: 'spring',
                stiffness: 200,
                damping: 15,
              },
            },
          }}
          whileHover={{
            scale: 1.1,
            rotate: 5,
            transition: { duration: 0.2 },
          }}
          whileTap={{ scale: 0.95 }}
        >
          <Github
            size={24}
            color={
              mounted ? getSelectedColorValue(selectedColor) : 'var(--blue)'
            }
            className="transition-all duration-300 ease-out"
          />
        </motion.a>
        <motion.a
          href="https://www.linkedin.com/in/veerendranathp/"
          target="_blank"
          rel="noopener noreferrer"
          className="transition-all duration-300 ease-out hover:opacity-80"
          variants={{
            hidden: { opacity: 0, scale: 0.8, rotate: 10 },
            visible: {
              opacity: 1,
              scale: 1,
              rotate: 0,
              transition: {
                duration: 0.5,
                ease: 'easeOut',
                type: 'spring',
                stiffness: 200,
                damping: 15,
              },
            },
          }}
          whileHover={{
            scale: 1.1,
            rotate: -5,
            transition: { duration: 0.2 },
          }}
          whileTap={{ scale: 0.95 }}
        >
          <Linkedin
            size={24}
            color={
              mounted ? getSelectedColorValue(selectedColor) : 'var(--blue)'
            }
            className="transition-all duration-300 ease-out"
          />
        </motion.a>
      </motion.div>

      {/* Horizontal Line */}
      <hr className="mt-6 border-[var(--white-feint-1)] opacity-30" />

      {/* Goodreads Widget */}
      <GoodreadsWidget />
    </div>
  );
}
