@import "tailwindcss";

:root {
  --white: #fff;
  --white-feint-1: rgba(255, 255, 255, 0.6);
  --white-feint-2: #848484;
  --white-feint-3: #898d8e;

  --black-1: #080808;
  --black-2: #0b1012;

  --yellow: #fff42b;
  --blue: #83e6f7;
  --green: #8df0cc;

  --hue-0: var(--yellow);
  --hue-1: #ff0088;
  --hue-2: #dd00ee;
  --hue-3: #9911ff;
  --hue-4: #1e75f7;
  --hue-5: #0cdcf7;
  --hue-6: var(--green);
  --hue-0-transparent: #fff31244;
  --hue-1-transparent: #ff008844;
  --hue-2-transparent: #dd00ee44;
  --hue-3-transparent: #9911ff44;
  --hue-4-transparent: #8ac0ff44;
  --hue-5-transparent: #0cdcf744;
  --hue-6-transparent: #8df0cc44;
  --text: var(--white);
  --accent: var(--blue);
  --accent-transparent: #83e6f71d;
  --primary-control-color: var(--white);
  --layer: #0b1011;
  --border: rgba(158, 234, 247, 0.15);
  --secondary-control-color: #8ac0ff0d;
  --background: var(--black);
  --layer-transparent: #14171caa;
  --divider: #1a1e26;
  --feint-text: #586d8c;
  --font-atkinson: var(--font-atkinson-hyperlegible);
  --font-rammetto: var(--font-rammetto-one);
  --font-azeret-mono: var(--font-azeret-mono);
  --font-mono: var(--font-azeret-mono);
  background: var(--black);
  color: var(--color);
  min-height: 100vh;
  min-height: 100svh;
  --dot-size: 1px;
  --gap-size: 20px;
  --offset: calc(var(--gap-size) / 2);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
