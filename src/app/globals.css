@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Color system matching reference */
  --color-zinc-50: #fafafa;
  --color-zinc-100: #f4f4f5;
  --color-zinc-200: #e4e4e7;
  --color-zinc-300: #d4d4d8;
  --color-zinc-400: #a1a1aa;
  --color-zinc-500: #71717a;
  --color-zinc-600: #52525b;
  --color-zinc-700: #3f3f46;
  --color-zinc-800: #27272a;
  --color-zinc-900: #18181b;

  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  --color-blue-300: #93c5fd;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;

  --color-neutral-200: #e5e5e5;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;

  /* Spacing and layout */
  --spacing: 0.25rem;
  --container-lg: 32rem;
  --container-2xl: 42rem;
  --container-prose: 65ch;

  /* Typography */
  --font-sans: var(--font-atkinson);
  --font-mono: var(--font-geist-mono);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-atkinson);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #080808;
    --foreground: #ededed;

    /* Override zinc colors for dark mode */
    --color-zinc-50: #18181b;
    --color-zinc-100: #27272a;
    --color-zinc-200: #3f3f46;
    --color-zinc-300: #52525b;
    --color-zinc-400: #71717a;
    --color-zinc-500: #a1a1aa;
    --color-zinc-600: #d4d4d8;
    --color-zinc-700: #e4e4e7;
    --color-zinc-800: #f4f4f5;
    --color-zinc-900: #fafafa;

    /* Override gray colors for dark mode */
    --color-gray-300: #4b5563;
    --color-gray-400: #6b7280;
    --color-gray-500: #9ca3af;
    --color-gray-600: #d1d5db;
    --color-gray-700: #e5e7eb;
    --color-gray-800: #f3f4f6;
    --color-gray-900: #f9fafb;
    --color-gray-950: #ffffff;

    --white: #fff;
    --white-feint: #898d8e;
    --bg-primary: #080808;
    --bg-secondary: #0b1012;
    --card-1: #121212;
    --card-2: #10161a;

    /* --black: #0b1012; */
    /* --black-2: rgba(131, 230, 247, 0.03); */
    --yellow: #fff42b;
    --blue: #83e6f7;
    --green: #8df0cc;
    --hue-0: var(--yellow);
    --hue-1: #ff0088;
    --hue-2: #dd00ee;
    --hue-3: #9911ff;
    --hue-4: #1e75f7;
    --hue-5: #0cdcf7;
    --hue-6: var(--green);
    --hue-0-transparent: #fff31244;
    --hue-1-transparent: #ff008844;
    --hue-2-transparent: #dd00ee44;
    --hue-3-transparent: #9911ff44;
    --hue-4-transparent: #8ac0ff44;
    --hue-5-transparent: #0cdcf744;
    --hue-6-transparent: #8df0cc44;
    --text: var(--white);
    --accent: var(--blue);
    --accent-transparent: #83e6f71d;
    --primary-control-color: var(--white);
    --layer: #0b1011;
    --border: rgba(158, 234, 247, 0.15);
    --secondary-control-color: #8ac0ff0d;
    --layer-transparent: #14171caa;
    --divider: #1a1e26;
    --feint-text: #586d8c;

    /* Typography scale */
    --text-xs: 0.75rem; /* 12px */
    --text-sm: 0.875rem; /* 14px */
    --text-base: 1rem; /* 16px */
    --text-lg: 1.125rem; /* 18px */
    --text-xl: 1.25rem; /* 20px */
    --text-2xl: 1.5rem; /* 24px */
    --text-3xl: 1.875rem; /* 30px */
    --text-4xl: 2.25rem; /* 36px */
    --text-5xl: 3rem; /* 48px */
    --text-6xl: 3.75rem; /* 60px */
    --text-7xl: 4.5rem; /* 72px */
    --text-8xl: 6rem; /* 96px */
    --text-9xl: 8rem; /* 128px */
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), ui-sans-serif, system-ui, sans-serif;
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  overflow-x: clip;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Page transition animations */
@keyframes slide-up {
  0% {
    opacity: 0;
    filter: blur(4px);
    transform: translateY(8px);
  }
  100% {
    opacity: 1;
    filter: blur(0px);
    transform: translateY(0);
  }
}

@keyframes slide-down {
  0% {
    opacity: 1;
    filter: blur(0px);
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    filter: blur(4px);
    transform: translateY(-8px);
  }
}

/* View Transitions API */
::view-transition-old(root) {
  animation: 250ms cubic-bezier(0.4, 0, 0.2, 1) forwards slide-down;
}

::view-transition-new(root) {
  animation: 250ms cubic-bezier(0.4, 0, 0.2, 1) forwards slide-up;
}

/* Fallback for browsers without View Transitions */
.page-transition-enter {
  animation: slide-up 250ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.page-transition-exit {
  animation: slide-down 250ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Ensure smooth transitions */
.page-transition-enter,
.page-transition-exit {
  will-change: transform, opacity, filter;
}

/* Additional utility classes for smooth transitions */
.blur-none {
  filter: blur(0);
}

.blur-sm {
  filter: blur(4px);
}

/* Utility classes matching reference design */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.max-w-lg {
  max-width: var(--container-lg);
}

.max-w-prose {
  max-width: var(--container-prose);
}

.text-zinc-900 {
  color: var(--color-zinc-900);
}

.text-zinc-500 {
  color: var(--color-zinc-500);
}

.text-zinc-200 {
  color: var(--color-zinc-200);
}

.dark .text-zinc-900 {
  color: var(--color-zinc-100);
}

.dark .text-zinc-500 {
  color: var(--color-zinc-400);
}

.dark .text-zinc-200 {
  color: var(--color-zinc-800);
}

.bg-zinc-900 {
  background-color: var(--color-zinc-900);
}

.dark .bg-zinc-900 {
  background-color: var(--color-zinc-900);
}
