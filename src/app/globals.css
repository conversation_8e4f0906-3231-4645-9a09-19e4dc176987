@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --white: #fff;
    --white-feint: #898d8e;
    --bg-primary: #080808;
    --bg-secondary: #0b1012;
    --card-1: #121212;
    --card-2: #10161a;

    /* --black: #0b1012; */
    /* --black-2: rgba(131, 230, 247, 0.03); */
    --yellow: #fff42b;
    --blue: #83e6f7;
    --green: #8df0cc;
    --hue-0: var(--yellow);
    --hue-1: #ff0088;
    --hue-2: #dd00ee;
    --hue-3: #9911ff;
    --hue-4: #1e75f7;
    --hue-5: #0cdcf7;
    --hue-6: var(--green);
    --hue-0-transparent: #fff31244;
    --hue-1-transparent: #ff008844;
    --hue-2-transparent: #dd00ee44;
    --hue-3-transparent: #9911ff44;
    --hue-4-transparent: #8ac0ff44;
    --hue-5-transparent: #0cdcf744;
    --hue-6-transparent: #8df0cc44;
    --text: var(--white);
    --accent: var(--blue);
    --accent-transparent: #83e6f71d;
    --primary-control-color: var(--white);
    --layer: #0b1011;
    --border: rgba(158, 234, 247, 0.15);
    --secondary-control-color: #8ac0ff0d;
    --layer-transparent: #14171caa;
    --divider: #1a1e26;
    --feint-text: #586d8c;

    /* Typography scale */
    --text-xs: 0.75rem; /* 12px */
    --text-sm: 0.875rem; /* 14px */
    --text-base: 1rem; /* 16px */
    --text-lg: 1.125rem; /* 18px */
    --text-xl: 1.25rem; /* 20px */
    --text-2xl: 1.5rem; /* 24px */
    --text-3xl: 1.875rem; /* 30px */
    --text-4xl: 2.25rem; /* 36px */
    --text-5xl: 3rem; /* 48px */
    --text-6xl: 3.75rem; /* 60px */
    --text-7xl: 4.5rem; /* 72px */
    --text-8xl: 6rem; /* 96px */
    --text-9xl: 8rem; /* 128px */
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Page transition animations */
@keyframes slide-up {
  0% {
    opacity: 0;
    filter: blur(4px);
    transform: translateY(8px);
  }
  100% {
    opacity: 1;
    filter: blur(0px);
    transform: translateY(0);
  }
}

@keyframes slide-down {
  0% {
    opacity: 1;
    filter: blur(0px);
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    filter: blur(4px);
    transform: translateY(-8px);
  }
}

/* View Transitions API */
::view-transition-old(root) {
  animation: 250ms cubic-bezier(0.4, 0, 0.2, 1) forwards slide-down;
}

::view-transition-new(root) {
  animation: 250ms cubic-bezier(0.4, 0, 0.2, 1) forwards slide-up;
}

/* Fallback for browsers without View Transitions */
.page-transition-enter {
  animation: slide-up 250ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.page-transition-exit {
  animation: slide-down 250ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}
