// Simple dynamic Goodreads data fetching
import { XMLParser } from 'fast-xml-parser';

export const GOODREADS_USER_ID: string = '162391550';

interface Book {
  id: string;
  title: string;
  author: string;
  authorId: string;
  coverUrl: string;
  goodreadsUrl: string;
  isbn: string;
}

interface ApiResult {
  success: boolean;
  data?: Book[];
  error?: string;
}

interface GoodreadsItem {
  book_id?: string;
  title?: string;
  author_name?: string;
  book_image_url?: string;
  link?: string;
  isbn?: string;
}

interface GoodreadsChannel {
  item?: GoodreadsItem | GoodreadsItem[];
}

interface GoodreadsRSS {
  channel?: GoodreadsChannel;
}

interface ParsedXML {
  rss?: GoodreadsRSS;
}

// Function to fetch books from Goodreads RSS feed
export async function fetchCurrentlyReadingBooks(): Promise<ApiResult> {
  try {
    // Use CORS proxy to fetch Goodreads RSS feed
    const corsProxy: string = 'https://api.allorigins.win/raw?url=';
    const goodreadsRssUrl: string = `https://www.goodreads.com/review/list_rss/${GOODREADS_USER_ID}?shelf=currently-reading`;

    const response: Response = await fetch(
      corsProxy + encodeURIComponent(goodreadsRssUrl)
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch books: ${response.status}`);
    }

    const xmlText: string = await response.text();
    const books: Book[] = parseGoodreadsRSS(xmlText);
    console.log(books);
    return {
      success: true,
      data: books,
    };
  } catch (error) {
    console.error('Error fetching books:', error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

// Simple XML parser for Goodreads RSS using fast-xml-parser
function parseGoodreadsRSS(xmlString: string): Book[] {
  try {
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@_',
    });

    const jsonObj: ParsedXML = parser.parse(xmlString);

    // Navigate to the items array
    const items: GoodreadsItem | GoodreadsItem[] | undefined =
      jsonObj.rss?.channel?.item;

    if (!items) {
      console.log('No items found in RSS feed');
      return [];
    }

    // Handle single item (not in array) vs multiple items (array)
    const itemsArray: GoodreadsItem[] = Array.isArray(items) ? items : [items];

    const books: Book[] = itemsArray.map((item: GoodreadsItem) => ({
      id: item.book_id || '',
      title: item.title || '',
      author: item.author_name || 'Unknown Author',
      authorId: '', // Not needed for display
      coverUrl: item.book_image_url || '',
      goodreadsUrl: item.link || '',
      isbn: item.isbn || '',
    }));

    console.log('Parsed books:', books);
    return books;
  } catch (error) {
    console.error('Error parsing RSS with fast-xml-parser:', error);
    return [];
  }
}
